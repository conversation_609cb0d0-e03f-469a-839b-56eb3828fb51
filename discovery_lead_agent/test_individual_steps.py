"""
Test Individual Steps of the Discovery Lead Agent
This script tests each component separately to verify functionality
"""

import json
from demo_agent import DemoDiscoveryAgent
import time

def test_step_1_discovery():
    """Test Step 1: Lead Discovery"""
    print("🔍 TESTING STEP 1: LEAD DISCOVERY")
    print("=" * 50)
    
    agent = DemoDiscoveryAgent()
    
    # Test with a single search query
    test_query = "dental service organization DSO multiple locations"
    print(f"Testing search query: {test_query}")
    
    try:
        from googlesearch import search
        urls = list(search(test_query, num_results=3, sleep_interval=1))
        
        print(f"✅ Found {len(urls)} URLs:")
        for i, url in enumerate(urls, 1):
            print(f"   {i}. {url}")
            
        return True
    except Exception as e:
        print(f"❌ Error in discovery: {e}")
        return False

def test_step_2_website_scraping():
    """Test Step 2: Website Scraping"""
    print("\n🔍 TESTING STEP 2: WEBSITE SCRAPING")
    print("=" * 50)
    
    agent = DemoDiscoveryAgent()
    
    # Test with a known dental website
    test_url = "https://www.advantagedental.com"
    print(f"Testing website scraping: {test_url}")
    
    try:
        website_info = agent.scrape_website(test_url)
        
        print("✅ Website scraping successful:")
        print(f"   Title: {website_info.get('title', 'N/A')[:50]}...")
        print(f"   Description: {website_info.get('description', 'N/A')[:50]}...")
        print(f"   Locations found: {len(website_info.get('locations', []))}")
        print(f"   Emails found: {len(website_info.get('contact_info', {}).get('emails', []))}")
        print(f"   Phones found: {len(website_info.get('contact_info', {}).get('phones', []))}")
        
        return True
    except Exception as e:
        print(f"❌ Error in website scraping: {e}")
        return False

def test_step_3_qualification():
    """Test Step 3: Lead Qualification"""
    print("\n🔍 TESTING STEP 3: LEAD QUALIFICATION")
    print("=" * 50)
    
    agent = DemoDiscoveryAgent()
    
    # Test qualification with sample data
    sample_website_info = {
        'title': 'Bright Dental Group - Multiple Locations',
        'description': 'Leading dental service organization with 15 locations',
        'locations': ['15 locations', 'california', 'texas'],
        'contact_info': {'emails': ['<EMAIL>'], 'phones': ['************']}
    }
    
    sample_company = {'domain': 'brightdental.com'}
    
    try:
        is_qualified = agent.qualify_company(sample_website_info, sample_company)
        score = agent.calculate_qualification_score(sample_website_info)
        
        print("✅ Qualification testing successful:")
        print(f"   Is qualified: {is_qualified}")
        print(f"   Qualification score: {score}/100")
        
        return True
    except Exception as e:
        print(f"❌ Error in qualification: {e}")
        return False

def test_step_4_email_generation():
    """Test Step 4: Email Generation"""
    print("\n🔍 TESTING STEP 4: EMAIL GENERATION")
    print("=" * 50)
    
    agent = DemoDiscoveryAgent()
    
    # Test email generation with sample lead
    sample_lead = {
        'company_name': 'Bright Dental Group',
        'estimated_locations': 15,
        'contact_info': {'emails': ['<EMAIL>']},
        'qualification_score': 85
    }
    
    try:
        email_content = agent.create_demo_email(sample_lead)
        
        print("✅ Email generation successful:")
        print(f"   Email length: {len(email_content)} characters")
        print(f"   Word count: {len(email_content.split())} words")
        print("\n📧 Sample Email:")
        print("-" * 40)
        print(email_content[:200] + "..." if len(email_content) > 200 else email_content)
        print("-" * 40)
        
        return True
    except Exception as e:
        print(f"❌ Error in email generation: {e}")
        return False

def test_step_5_data_processing():
    """Test Step 5: Data Processing"""
    print("\n🔍 TESTING STEP 5: DATA PROCESSING")
    print("=" * 50)
    
    agent = DemoDiscoveryAgent()
    
    try:
        # Test domain extraction
        test_url = "https://www.example-dental.com/about"
        domain = agent.extract_domain(test_url)
        print(f"✅ Domain extraction: {test_url} → {domain}")
        
        # Test potential dental company detection
        test_url_dental = "https://bright-dental-group.com"
        is_dental = agent.is_potential_dental_company(test_url_dental)
        print(f"✅ Dental detection: {test_url_dental} → {is_dental}")
        
        return True
    except Exception as e:
        print(f"❌ Error in data processing: {e}")
        return False

def test_step_6_full_integration():
    """Test Step 6: Full Integration Test"""
    print("\n🔍 TESTING STEP 6: FULL INTEGRATION")
    print("=" * 50)
    
    try:
        # Run a mini version of the full process
        agent = DemoDiscoveryAgent()
        agent.target_lead_count = 2  # Reduce for testing
        
        print("Running mini discovery process...")
        leads = agent.run_discovery_process()
        
        print(f"✅ Full integration test successful:")
        print(f"   Leads found: {len(leads)}")
        
        if leads:
            sample_lead = leads[0]
            print(f"   Sample lead: {sample_lead['company_name'][:30]}...")
            print(f"   Qualification score: {sample_lead['qualification_score']}/100")
            print(f"   Has email: {'Yes' if sample_lead.get('demo_email') else 'No'}")
        
        return True
    except Exception as e:
        print(f"❌ Error in full integration: {e}")
        return False

def run_all_tests():
    """Run all individual step tests"""
    print("🧪 DISCOVERY LEAD AGENT - INDIVIDUAL STEP TESTING")
    print("=" * 60)
    
    tests = [
        ("Step 1: Lead Discovery", test_step_1_discovery),
        ("Step 2: Website Scraping", test_step_2_website_scraping),
        ("Step 3: Lead Qualification", test_step_3_qualification),
        ("Step 4: Email Generation", test_step_4_email_generation),
        ("Step 5: Data Processing", test_step_5_data_processing),
        ("Step 6: Full Integration", test_step_6_full_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"   ❌ FAILED: {e}")
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("🧪 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 OVERALL RESULTS: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The Discovery Lead Agent is working perfectly!")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
