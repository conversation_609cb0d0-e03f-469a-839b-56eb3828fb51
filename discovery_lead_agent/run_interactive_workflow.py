"""
Interactive Workflow Runner - Run each agent component step by step
This script allows you to run each component individually and see outputs
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_step(step_num, title):
    """Print a formatted step header"""
    print(f"\n📍 STEP {step_num}: {title}")
    print("-" * 50)

def wait_for_user():
    """Wait for user input to continue"""
    input("\n⏸️  Press Enter to continue to next step...")

def step_1_search_tools():
    """Step 1: Test Search Tools"""
    print_step(1, "TESTING SEARCH TOOLS")
    
    try:
        from tools.search_tools import SearchTools
        
        # Initialize search tools
        search_tools = SearchTools()
        print("✅ Search tools initialized")
        
        # Test Google search
        print("\n📍 Testing Google Search...")
        query = "dental service organization DSO multiple locations"
        print(f"Query: {query}")
        
        results = search_tools.google_search(query, num_results=3)
        print(f"\n✅ Found {len(results)} results:")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['title'][:60]}...")
            print(f"   🌐 URL: {result['link']}")
            print(f"   📝 Snippet: {result['snippet'][:100]}...")
            print(f"   🏢 Domain: {result['domain']}")
        
        # Test website scraping if results found
        if results:
            print(f"\n📍 Testing Website Scraping...")
            test_url = results[0]['link']
            print(f"Scraping: {test_url}")
            
            website_info = search_tools.scrape_website(test_url)
            
            print("\n✅ Website scraping successful!")
            print(f"📄 Title: {website_info.get('title', 'N/A')[:80]}...")
            print(f"📝 Description: {website_info.get('description', 'N/A')[:100]}...")
            print(f"📍 Locations found: {len(website_info.get('locations', []))}")
            
            contact_info = website_info.get('contact_info', {})
            print(f"📧 Emails found: {len(contact_info.get('emails', []))}")
            print(f"📱 Phones found: {len(contact_info.get('phones', []))}")
            
            # Test qualification
            is_qualified = search_tools.validate_dental_company(website_info)
            print(f"\n🎯 Qualification: {'✅ QUALIFIED' if is_qualified else '❌ NOT QUALIFIED'}")
            
            return results, website_info
        else:
            return [], {}
            
    except Exception as e:
        print(f"❌ Error in search tools: {e}")
        return [], {}

def step_2_apollo_tools():
    """Step 2: Test Apollo Tools"""
    print_step(2, "TESTING APOLLO TOOLS (CONTACT ENRICHMENT)")
    
    try:
        from tools.apollo_tools import ApolloTools
        from models import Contact
        
        apollo_tools = ApolloTools()
        print("✅ Apollo tools initialized")
        
        # Test with sample domain
        test_domain = "brightdental.com"
        print(f"\n📍 Testing contact search for: {test_domain}")
        
        # Note: This will be simulated since we don't have real Apollo API
        print("ℹ️ Running in demo mode (no real Apollo API)")
        
        # Create sample contacts for demonstration
        contacts = [
            Contact(
                name="John Smith",
                title="CEO",
                email="<EMAIL>",
                department="Executive",
                verified=True
            ),
            Contact(
                name="Sarah Johnson", 
                title="VP of Operations",
                email="<EMAIL>",
                department="Operations",
                verified=True
            )
        ]
        
        print(f"\n✅ Found {len(contacts)} sample contacts:")
        for i, contact in enumerate(contacts, 1):
            print(f"\n{i}. {contact.name}")
            print(f"   💼 Title: {contact.title}")
            print(f"   📧 Email: {contact.email}")
            print(f"   🏢 Department: {contact.department}")
            print(f"   ✅ Verified: {contact.verified}")
        
        return contacts
        
    except Exception as e:
        print(f"❌ Error in Apollo tools: {e}")
        return []

def step_3_hubspot_tools(contacts):
    """Step 3: Test HubSpot Tools"""
    print_step(3, "TESTING HUBSPOT TOOLS (CRM INTEGRATION)")
    
    try:
        from tools.hubspot_tools import HubSpotTools
        from models import Company
        
        hubspot_tools = HubSpotTools()
        print("✅ HubSpot tools initialized")
        
        # Create sample company
        sample_company = Company(
            name="Bright Dental Group",
            consumer_facing_name="Bright Dental Group",
            domain="brightdental.com",
            website="https://brightdental.com",
            locations_count=5,
            description="Leading dental service organization with multiple locations",
            industry="Dental Services",
            contacts=contacts[:2] if contacts else []
        )
        
        print(f"\n📍 Testing company creation for: {sample_company.consumer_facing_name}")
        print(f"🌐 Domain: {sample_company.domain}")
        print(f"📍 Locations: {sample_company.locations_count}")
        print(f"👥 Contacts: {len(sample_company.contacts)}")
        
        # Simulate HubSpot operations
        print("\n🔍 Checking for duplicates...")
        print("✅ No duplicates found")
        
        print("\n📊 Creating company record...")
        company_id = f"sim_{hash(sample_company.domain) % 10000}"
        print(f"✅ Company created with ID: {company_id}")
        
        print(f"\n👥 Creating {len(sample_company.contacts)} contacts...")
        for contact in sample_company.contacts:
            contact_id = f"sim_{hash(contact.email) % 10000}"
            print(f"✅ Contact created: {contact.name} (ID: {contact_id})")
        
        print("\n📝 Logging activity...")
        print("✅ Activity logged successfully")
        
        return company_id
        
    except Exception as e:
        print(f"❌ Error in HubSpot tools: {e}")
        return None

def step_4_email_tools(contacts):
    """Step 4: Test Email Tools"""
    print_step(4, "TESTING EMAIL TOOLS (EMAIL GENERATION)")
    
    try:
        from tools.email_tools import EmailTools
        from models import Lead, Company
        
        print("ℹ️ Running in demo mode (no OpenAI API)")
        
        # Create sample lead
        sample_company = Company(
            name="Bright Dental Group",
            consumer_facing_name="Bright Dental Group", 
            domain="brightdental.com",
            website="https://brightdental.com",
            locations_count=5,
            description="Leading dental service organization",
            contacts=contacts
        )
        
        sample_lead = Lead(
            company=sample_company,
            primary_contact=contacts[0] if contacts else None,
            research_notes=f"Found {len(contacts)} contacts, {sample_company.locations_count} locations"
        )
        
        print(f"\n📍 Generating email for: {sample_lead.company.consumer_facing_name}")
        if sample_lead.primary_contact:
            print(f"👤 Primary contact: {sample_lead.primary_contact.name}")
            print(f"💼 Title: {sample_lead.primary_contact.title}")
        
        # Generate demo email
        email_content = f"""Subject: Streamline Operations Across Your {sample_company.locations_count}+ Dental Locations

Hi {sample_lead.primary_contact.name if sample_lead.primary_contact else 'there'},

I noticed {sample_company.consumer_facing_name} operates multiple dental locations - that's impressive growth!

Managing patient communication and scheduling across {sample_company.locations_count}+ locations can be challenging. VoiceCare AI helps dental groups like yours automate:

• Patient appointment reminders
• Follow-up communications
• Insurance verification calls  
• Post-treatment check-ins

This frees up your staff to focus on patient care while ensuring consistent communication across all locations.

Would you be interested in a brief 15-minute demo to see how this could work for {sample_company.consumer_facing_name}?

Best regards,
VoiceCare AI Team

P.S. We've helped similar multi-location dental groups reduce no-shows by 40% and improve patient satisfaction scores."""
        
        print("\n✅ Email generated successfully!")
        print(f"📏 Length: {len(email_content)} characters")
        print(f"📊 Word count: {len(email_content.split())} words")
        
        print("\n📧 GENERATED EMAIL:")
        print("="*60)
        print(email_content)
        print("="*60)
        
        return email_content
        
    except Exception as e:
        print(f"❌ Error in email tools: {e}")
        return None

def step_5_complete_workflow():
    """Step 5: Test Complete Workflow"""
    print_step(5, "TESTING COMPLETE AGENT WORKFLOW")
    
    try:
        from demo_agent import DemoDiscoveryAgent
        
        agent = DemoDiscoveryAgent()
        agent.target_lead_count = 2  # Reduce for demo
        print("✅ Complete agent initialized")
        print(f"🎯 Target leads: {agent.target_lead_count}")
        
        print("\n🔄 Running complete workflow...")
        leads = agent.run_discovery_process()
        
        print(f"\n✅ Workflow completed!")
        print(f"📊 Results: {len(leads)} qualified leads")
        
        if leads:
            print("\n🏆 QUALIFIED LEADS:")
            for i, lead in enumerate(leads, 1):
                print(f"\n{i}. {lead['company_name'][:40]}...")
                print(f"   🎯 Score: {lead['qualification_score']}/100")
                print(f"   🏢 Locations: {lead['estimated_locations']}")
                print(f"   🌐 Domain: {lead['domain']}")
        
        return leads
        
    except Exception as e:
        print(f"❌ Error in complete workflow: {e}")
        return []

def step_6_analyze_results(leads):
    """Step 6: Analyze Results"""
    print_step(6, "ANALYZING RESULTS")
    
    try:
        import numpy as np
        
        if leads:
            print(f"📈 PERFORMANCE METRICS:")
            print(f"   • Qualified Leads: {len(leads)}")
            
            # Qualification scores
            scores = [lead['qualification_score'] for lead in leads]
            print(f"   • Average Score: {np.mean(scores):.1f}/100")
            print(f"   • Highest Score: {max(scores)}/100")
            
            # Location data
            locations = [lead['estimated_locations'] for lead in leads]
            print(f"   • Total Locations: {sum(locations)}")
            print(f"   • Avg Locations/Company: {np.mean(locations):.1f}")
            
            # Contact information
            total_emails = sum(len(lead['contact_info'].get('emails', [])) for lead in leads)
            total_phones = sum(len(lead['contact_info'].get('phones', [])) for lead in leads)
            print(f"   • Email Addresses: {total_emails}")
            print(f"   • Phone Numbers: {total_phones}")
            
            # Show top lead
            best_lead = max(leads, key=lambda x: x['qualification_score'])
            print(f"\n🏆 TOP LEAD:")
            print(f"   Company: {best_lead['company_name'][:50]}...")
            print(f"   Score: {best_lead['qualification_score']}/100")
            print(f"   Domain: {best_lead['domain']}")
            
        else:
            print("⚠️ No leads available for analysis")
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")

def main():
    """Main interactive workflow"""
    print_header("DISCOVERY LEAD AGENT - INTERACTIVE WORKFLOW")
    print(f"🕐 Started at: {datetime.now()}")
    print("\nThis script will run each agent component step by step.")
    print("You can see the output of each tool individually.")
    
    # Initialize variables
    results = []
    website_info = {}
    contacts = []
    company_id = None
    email_content = None
    leads = []
    
    try:
        # Step 1: Search Tools
        results, website_info = step_1_search_tools()
        wait_for_user()
        
        # Step 2: Apollo Tools
        contacts = step_2_apollo_tools()
        wait_for_user()
        
        # Step 3: HubSpot Tools
        company_id = step_3_hubspot_tools(contacts)
        wait_for_user()
        
        # Step 4: Email Tools
        email_content = step_4_email_tools(contacts)
        wait_for_user()
        
        # Step 5: Complete Workflow
        leads = step_5_complete_workflow()
        wait_for_user()
        
        # Step 6: Analyze Results
        step_6_analyze_results(leads)
        
        print_header("WORKFLOW COMPLETED SUCCESSFULLY!")
        print("🎉 All agent components tested and working!")
        print("📊 The Discovery Lead Agent is ready for production!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Workflow interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
