"""
Main entry point for the Discovery Lead Agent
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent import DiscoveryLeadAgent
from config import Config

def main():
    """
    Main function to run the Discovery Lead Agent
    """
    print("🤖 VoiceCare AI - Discovery Lead Agent")
    print("=" * 50)
    
    # Validate configuration
    config = Config()
    if not validate_config(config):
        print("❌ Configuration validation failed. Please check your .env file.")
        return
    
    # Initialize and run agent
    try:
        agent = DiscoveryLeadAgent()
        leads = agent.run_discovery_process()
        
        if leads:
            print(f"\n🎉 SUCCESS! Discovered {len(leads)} qualified leads.")
            print("\nNext Steps:")
            print("1. Review the generated emails in your drafts")
            print("2. Check HubSpot for new company and contact records")
            print("3. Review the results JSON file for detailed information")
            print("4. Begin outreach to qualified leads")
        else:
            print("\n⚠️ No qualified leads found. Consider adjusting search criteria.")
            
    except Exception as e:
        print(f"\n❌ Error running agent: {e}")
        print("Please check your configuration and try again.")

def validate_config(config: Config) -> bool:
    """
    Validate that all required configuration is present
    """
    required_keys = [
        'OPENAI_API_KEY',
        'HUBSPOT_API_KEY',
        'APOLLO_API_KEY',
        'GOOGLE_API_KEY',
        'GOOGLE_CSE_ID'
    ]
    
    missing_keys = []
    for key in required_keys:
        if not getattr(config, key):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing required configuration keys: {', '.join(missing_keys)}")
        print("Please update your .env file with the required API keys.")
        return False
    
    return True

def setup_instructions():
    """
    Print setup instructions for first-time users
    """
    print("\n📋 SETUP INSTRUCTIONS:")
    print("=" * 30)
    print("1. Copy .env.example to .env")
    print("2. Fill in your API keys:")
    print("   - OpenAI API Key")
    print("   - HubSpot API Key")
    print("   - Apollo API Key")
    print("   - Google API Key & Custom Search Engine ID")
    print("3. Install dependencies: pip install -r requirements.txt")
    print("4. Run: python main.py")
    print("\n🔗 API Key Sources:")
    print("   - OpenAI: https://platform.openai.com/api-keys")
    print("   - HubSpot: https://app.hubspot.com/developer")
    print("   - Apollo: https://app.apollo.io/settings/integrations")
    print("   - Google: https://console.developers.google.com/")

if __name__ == "__main__":
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️ No .env file found!")
        setup_instructions()
    else:
        main()
