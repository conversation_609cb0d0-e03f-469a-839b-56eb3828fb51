"""
Main Discovery Lead Agent using LangChain
"""
from langchain.agents import initialize_agent, AgentType
from langchain.llms import OpenAI
from langchain.tools import Tool
from langchain.memory import ConversationBufferMemory
from typing import List, Dict, Optional
import json
import time
from datetime import datetime

from config import Config
from models import Company, Contact, Lead, AgentLog
from tools.search_tools import SearchTools
from tools.hubspot_tools import HubSpotTools
from tools.apollo_tools import ApolloTools
from tools.email_tools import EmailTools

class DiscoveryLeadAgent:
    def __init__(self):
        self.config = Config()
        self.llm = OpenAI(
            openai_api_key=self.config.OPENAI_API_KEY,
            temperature=0.3
        )
        
        # Initialize tools
        self.search_tools = SearchTools()
        self.hubspot_tools = HubSpotTools()
        self.apollo_tools = ApolloTools()
        self.email_tools = EmailTools()
        
        # Initialize memory and logs
        self.memory = ConversationBufferMemory(memory_key="chat_history")
        self.leads: List[Lead] = []
        self.logs: List[AgentLog] = []
        
        # Setup LangChain agent
        self.setup_agent()
    
    def setup_agent(self):
        """
        Setup LangChain agent with custom tools
        """
        tools = [
            Tool(
                name="discover_leads",
                description="Discover potential dental organization leads using Google search",
                func=self.discover_leads_tool
            ),
            Tool(
                name="research_company",
                description="Research a company's website for detailed information",
                func=self.research_company_tool
            ),
            Tool(
                name="enrich_contacts",
                description="Enrich contact information using Apollo API",
                func=self.enrich_contacts_tool
            ),
            Tool(
                name="create_hubspot_records",
                description="Create company and contact records in HubSpot",
                func=self.create_hubspot_records_tool
            ),
            Tool(
                name="generate_email",
                description="Generate personalized email for a lead",
                func=self.generate_email_tool
            )
        ]
        
        self.agent = initialize_agent(
            tools=tools,
            llm=self.llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True
        )
    
    def run_discovery_process(self) -> List[Lead]:
        """
        Run the complete 6-step discovery process
        """
        print("🚀 Starting Discovery Lead Agent Process...")
        
        try:
            # Step 1: Discovery - Identify Potential Leads
            print("\n📍 Step 1: Discovering potential leads...")
            discovered_companies = self.discover_leads()
            
            # Step 2 & 3: Company Research and Contact Enrichment
            print(f"\n🔍 Step 2-3: Researching {len(discovered_companies)} companies...")
            qualified_leads = []
            
            for company_data in discovered_companies:
                lead = self.research_and_enrich_company(company_data)
                if lead:
                    qualified_leads.append(lead)
                    
                    # Stop when we reach target count
                    if len(qualified_leads) >= self.config.TARGET_LEAD_COUNT:
                        break
            
            # Step 4: Create HubSpot Records
            print(f"\n📊 Step 4: Creating HubSpot records for {len(qualified_leads)} leads...")
            for lead in qualified_leads:
                self.create_hubspot_records(lead)
            
            # Step 5: Email Drafting
            print(f"\n✉️ Step 5: Generating emails for {len(qualified_leads)} leads...")
            for lead in qualified_leads:
                self.generate_and_save_email(lead)
            
            # Step 6: Logging and Auditing
            print(f"\n📝 Step 6: Logging activities...")
            self.log_final_results(qualified_leads)
            
            self.leads = qualified_leads
            print(f"\n✅ Discovery process completed! Found {len(qualified_leads)} qualified leads.")
            
            return qualified_leads
            
        except Exception as e:
            self.log_error("discovery_process", str(e))
            print(f"❌ Error in discovery process: {e}")
            return []
        
        finally:
            # Cleanup
            self.search_tools.close_driver()
    
    def discover_leads(self) -> List[Dict]:
        """
        Step 1: Discover potential leads using Google search
        """
        discovered_companies = []
        
        for query in self.config.SEARCH_QUERIES:
            print(f"Searching: {query}")
            results = self.search_tools.google_search(query, num_results=20)
            
            for result in results:
                if self.is_potential_dental_company(result):
                    discovered_companies.append(result)
            
            time.sleep(1)  # Rate limiting
        
        # Remove duplicates based on domain
        unique_companies = {}
        for company in discovered_companies:
            domain = company.get('domain', '')
            if domain and domain not in unique_companies:
                unique_companies[domain] = company
        
        self.log_activity("discover_leads", {
            "total_found": len(list(unique_companies.values())),
            "queries_used": len(self.config.SEARCH_QUERIES)
        })
        
        return list(unique_companies.values())[:50]  # Limit to 50 for processing
    
    def research_and_enrich_company(self, company_data: Dict) -> Optional[Lead]:
        """
        Steps 2-3: Research company and enrich contacts
        """
        try:
            url = company_data.get('link', '')
            domain = company_data.get('domain', '')
            
            if not url or not domain:
                return None
            
            # Research company website
            print(f"Researching: {domain}")
            website_info = self.search_tools.scrape_website(url)
            
            # Validate if it's a qualified dental company
            if not self.search_tools.validate_dental_company(website_info):
                print(f"❌ {domain} - Not qualified (insufficient locations or not dental)")
                return None
            
            # Create company object
            company = Company(
                name=company_data.get('title', ''),
                consumer_facing_name=self.clean_company_name(company_data.get('title', '')),
                domain=domain,
                website=url,
                locations_count=len(website_info.get('locations', [])),
                description=website_info.get('description', ''),
                states_served=self.extract_states(website_info)
            )
            
            # Enrich contacts using Apollo
            print(f"Enriching contacts for: {domain}")
            contacts = self.apollo_tools.search_people_by_domain(domain)
            
            if not contacts:
                print(f"❌ {domain} - No qualified contacts found")
                return None
            
            company.contacts = contacts[:5]  # Limit to top 5 contacts
            
            # Create lead object
            lead = Lead(
                company=company,
                primary_contact=contacts[0] if contacts else None,
                research_notes=f"Found {len(contacts)} contacts, {company.locations_count} locations"
            )
            
            print(f"✅ {domain} - Qualified lead with {len(contacts)} contacts")
            return lead
            
        except Exception as e:
            print(f"❌ Error researching {company_data.get('domain', 'unknown')}: {e}")
            return None

    def create_hubspot_records(self, lead: Lead) -> bool:
        """
        Step 4: Create HubSpot records for company and contacts
        """
        try:
            # Check for duplicate company
            existing_company_id = self.hubspot_tools.check_duplicate_company(lead.company.domain)

            if existing_company_id:
                print(f"Company {lead.company.consumer_facing_name} already exists in HubSpot")
                lead.company.hubspot_id = existing_company_id
            else:
                # Create new company
                company_id = self.hubspot_tools.create_company(lead.company)
                if company_id:
                    lead.company.hubspot_id = company_id
                    print(f"✅ Created company: {lead.company.consumer_facing_name}")
                else:
                    print(f"❌ Failed to create company: {lead.company.consumer_facing_name}")
                    return False

            # Create contacts
            created_contacts = 0
            for contact in lead.company.contacts:
                if contact.email:
                    # Check for duplicate contact
                    existing_contact_id = self.hubspot_tools.check_duplicate_contact(contact.email)

                    if not existing_contact_id:
                        contact_id = self.hubspot_tools.create_contact(contact, lead.company.hubspot_id)
                        if contact_id:
                            created_contacts += 1
                            print(f"✅ Created contact: {contact.name}")
                        else:
                            print(f"❌ Failed to create contact: {contact.name}")
                    else:
                        print(f"Contact {contact.name} already exists in HubSpot")

            # Log activity
            self.hubspot_tools.log_activity(
                "companies",
                lead.company.hubspot_id,
                "Lead Discovery",
                f"Discovered via automated agent. {created_contacts} contacts added."
            )

            return True

        except Exception as e:
            print(f"❌ Error creating HubSpot records: {e}")
            return False

    def generate_and_save_email(self, lead: Lead) -> bool:
        """
        Step 5: Generate and save personalized email
        """
        try:
            if not lead.primary_contact or not lead.primary_contact.email:
                print(f"❌ No primary contact email for {lead.company.consumer_facing_name}")
                return False

            # Generate personalized email
            email_content = self.email_tools.generate_personalized_email(lead)

            if not email_content or "Error" in email_content:
                print(f"❌ Failed to generate email for {lead.company.consumer_facing_name}")
                return False

            # Validate email content
            if not self.email_tools.validate_email_content(email_content):
                print(f"❌ Email content validation failed for {lead.company.consumer_facing_name}")
                return False

            # Save email draft
            success = self.email_tools.save_email_draft(
                email_content,
                lead.primary_contact.email
            )

            if success:
                lead.email_draft = email_content
                print(f"✅ Email draft saved for {lead.primary_contact.name}")
                return True
            else:
                print(f"❌ Failed to save email draft for {lead.primary_contact.name}")
                return False

        except Exception as e:
            print(f"❌ Error generating email: {e}")
            return False

    def is_potential_dental_company(self, search_result: Dict) -> bool:
        """
        Quick validation if search result is potentially a dental company
        """
        text = (search_result.get('title', '') + ' ' + search_result.get('snippet', '')).lower()

        dental_keywords = ['dental', 'dentist', 'orthodontic', 'oral']
        location_keywords = ['locations', 'offices', 'practices', 'clinics', 'multiple']

        has_dental = any(keyword in text for keyword in dental_keywords)
        has_locations = any(keyword in text for keyword in location_keywords)

        return has_dental and has_locations

    def clean_company_name(self, name: str) -> str:
        """
        Clean company name by removing legal suffixes
        """
        legal_suffixes = [
            'LLC', 'Inc.', 'Inc', 'Corporation', 'Corp.', 'Corp',
            'Limited', 'Ltd.', 'Ltd', 'LP', 'LLP', 'PC', 'P.C.'
        ]

        cleaned_name = name
        for suffix in legal_suffixes:
            cleaned_name = cleaned_name.replace(f' {suffix}', '').replace(f', {suffix}', '')

        return cleaned_name.strip()

    def extract_states(self, website_info: Dict) -> List[str]:
        """
        Extract states served from website information
        """
        states = []
        locations_text = ' '.join(website_info.get('locations', []))

        # Common state abbreviations and names
        state_patterns = [
            'CA', 'NY', 'TX', 'FL', 'IL', 'PA', 'OH', 'GA', 'NC', 'MI',
            'California', 'New York', 'Texas', 'Florida', 'Illinois'
        ]

        for state in state_patterns:
            if state in locations_text:
                states.append(state)

        return list(set(states))[:5]  # Limit to 5 states

    def log_activity(self, action: str, details: Dict, success: bool = True, error: str = None):
        """
        Log agent activity
        """
        log_entry = AgentLog(
            action=action,
            details=details,
            success=success,
            error_message=error
        )
        self.logs.append(log_entry)

    def log_error(self, action: str, error: str):
        """
        Log error activity
        """
        self.log_activity(action, {}, success=False, error=error)

    def log_final_results(self, leads: List[Lead]):
        """
        Step 6: Log final results and statistics
        """
        stats = {
            "total_leads_found": len(leads),
            "companies_created": len([l for l in leads if l.company.hubspot_id]),
            "contacts_created": sum(len(l.company.contacts) for l in leads),
            "emails_generated": len([l for l in leads if l.email_draft]),
            "completion_time": datetime.now().isoformat()
        }

        self.log_activity("final_results", stats)

        print(f"\n📊 FINAL STATISTICS:")
        print(f"   • Total Qualified Leads: {stats['total_leads_found']}")
        print(f"   • Companies Created in HubSpot: {stats['companies_created']}")
        print(f"   • Contacts Created: {stats['contacts_created']}")
        print(f"   • Emails Generated: {stats['emails_generated']}")

        # Save results to file
        self.save_results_to_file(leads, stats)

    def save_results_to_file(self, leads: List[Lead], stats: Dict):
        """
        Save results to JSON file for review
        """
        try:
            results = {
                "statistics": stats,
                "leads": [
                    {
                        "company_name": lead.company.consumer_facing_name,
                        "domain": lead.company.domain,
                        "locations_count": lead.company.locations_count,
                        "contacts_count": len(lead.company.contacts),
                        "primary_contact": {
                            "name": lead.primary_contact.name if lead.primary_contact else None,
                            "title": lead.primary_contact.title if lead.primary_contact else None,
                            "email": lead.primary_contact.email if lead.primary_contact else None
                        },
                        "hubspot_id": lead.company.hubspot_id,
                        "email_generated": bool(lead.email_draft)
                    }
                    for lead in leads
                ]
            }

            filename = f"discovery_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2)

            print(f"📄 Results saved to: {filename}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

    # Tool functions for LangChain agent
    def discover_leads_tool(self, query: str) -> str:
        """Tool function for lead discovery"""
        results = self.discover_leads()
        return f"Discovered {len(results)} potential leads"

    def research_company_tool(self, domain: str) -> str:
        """Tool function for company research"""
        # Implementation would go here
        return f"Researched company: {domain}"

    def enrich_contacts_tool(self, domain: str) -> str:
        """Tool function for contact enrichment"""
        contacts = self.apollo_tools.search_people_by_domain(domain)
        return f"Found {len(contacts)} contacts for {domain}"

    def create_hubspot_records_tool(self, lead_data: str) -> str:
        """Tool function for HubSpot record creation"""
        # Implementation would go here
        return "HubSpot records created"

    def generate_email_tool(self, lead_data: str) -> str:
        """Tool function for email generation"""
        # Implementation would go here
        return "Email generated and saved as draft"
