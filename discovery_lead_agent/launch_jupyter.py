"""
Jupyter Notebook Launcher for Discovery Lead Agent
This script launches <PERSON><PERSON><PERSON> notebook and opens the interactive workflow
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def check_jupyter_installed():
    """Check if <PERSON><PERSON><PERSON> is installed"""
    try:
        subprocess.run([sys.executable, "-c", "import jupyter"], 
                      check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError:
        return False

def install_jupyter():
    """Install Jupyter if not present"""
    print("📦 Installing Jupyter notebook...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "jupyter"], 
                      check=True)
        print("✅ Jupyter installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Jupyter: {e}")
        return False

def launch_jupyter():
    """Launch Jupyter notebook"""
    print("🚀 Launching Jupyter notebook...")
    
    # Check if notebook exists
    notebook_path = Path("interactive_workflow_notebook.ipynb")
    if not notebook_path.exists():
        print("❌ Notebook file not found!")
        return False
    
    try:
        # Launch Jupyter notebook
        cmd = [
            sys.executable, "-m", "jupyter", "notebook", 
            str(notebook_path),
            "--no-browser",
            "--port=8888"
        ]
        
        print("📝 Starting Jupyter server...")
        print("🌐 Notebook will be available at: http://localhost:8888")
        print("📋 Copy the token from the output below to access the notebook")
        print("=" * 60)
        
        # Start the process
        process = subprocess.Popen(cmd)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Try to open browser
        try:
            webbrowser.open("http://localhost:8888")
            print("🌐 Browser opened automatically")
        except:
            print("⚠️ Could not open browser automatically")
            print("   Please open http://localhost:8888 manually")
        
        print("\n📖 JUPYTER NOTEBOOK INSTRUCTIONS:")
        print("=" * 50)
        print("1. Copy the token from the Jupyter output above")
        print("2. Open http://localhost:8888 in your browser")
        print("3. Paste the token when prompted")
        print("4. Run each cell step by step to see agent outputs")
        print("5. Press Ctrl+C here to stop the Jupyter server")
        
        # Wait for process
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n⏹️ Stopping Jupyter server...")
            process.terminate()
            process.wait()
            print("✅ Jupyter server stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to launch Jupyter: {e}")
        return False

def main():
    """Main function"""
    print("📓 DISCOVERY LEAD AGENT - JUPYTER LAUNCHER")
    print("=" * 50)
    
    # Check if Jupyter is installed
    if not check_jupyter_installed():
        print("⚠️ Jupyter not found. Installing...")
        if not install_jupyter():
            print("❌ Cannot proceed without Jupyter")
            return
    else:
        print("✅ Jupyter is already installed")
    
    # Launch Jupyter
    success = launch_jupyter()
    
    if success:
        print("\n🎉 Jupyter session completed!")
    else:
        print("\n❌ Failed to launch Jupyter")
        print("\n🔧 ALTERNATIVE: Run the interactive workflow directly:")
        print("   python run_interactive_workflow.py")

if __name__ == "__main__":
    main()
