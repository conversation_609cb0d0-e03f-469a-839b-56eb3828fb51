"""
Demo Discovery Lead Agent - Works without paid APIs
Uses free Google search and simulates other integrations
"""
import json
import time
from datetime import datetime
from typing import List, Dict, Optional
import requests
from bs4 import BeautifulSoup
from googlesearch import search
from urllib.parse import urlparse
from fake_useragent import UserAgent
import re

class DemoDiscoveryAgent:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random
        })
        
        # Demo configuration
        self.target_lead_count = 5  # Reduced for demo
        self.min_locations = 3
        
        # Search queries for dental organizations
        self.search_queries = [
            "dental service organization DSO multiple locations",
            "dental group practice multiple offices",
            "multi-location dental practice"
        ]
        
        # Results storage
        self.discovered_companies = []
        self.qualified_leads = []
        
    def run_discovery_process(self) -> List[Dict]:
        """
        Run the complete discovery process using free tools
        """
        print("🚀 Starting Demo Discovery Lead Agent Process...")
        print("=" * 60)
        
        try:
            # Step 1: Discovery
            print("\n📍 Step 1: Discovering potential leads using free Google search...")
            self.discover_leads()
            
            # Step 2-3: Research and qualify
            print(f"\n🔍 Step 2-3: Researching {len(self.discovered_companies)} companies...")
            self.research_and_qualify_leads()
            
            # Step 4: Simulate HubSpot integration
            print(f"\n📊 Step 4: Simulating HubSpot record creation...")
            self.simulate_hubspot_integration()
            
            # Step 5: Generate demo emails
            print(f"\n✉️ Step 5: Generating demo emails...")
            self.generate_demo_emails()
            
            # Step 6: Final reporting
            print(f"\n📝 Step 6: Generating final report...")
            self.generate_final_report()
            
            print(f"\n✅ Demo completed! Found {len(self.qualified_leads)} qualified leads.")
            return self.qualified_leads
            
        except Exception as e:
            print(f"❌ Error in discovery process: {e}")
            return []
    
    def discover_leads(self):
        """
        Step 1: Discover leads using free Google search
        """
        for query in self.search_queries:
            print(f"   Searching: {query}")
            try:
                # Use free Google search
                urls = search(query, num_results=10, sleep_interval=2, lang='en')
                
                for url in urls:
                    if len(self.discovered_companies) >= 20:  # Limit for demo
                        break
                        
                    try:
                        # Quick validation
                        if self.is_potential_dental_company(url):
                            domain = self.extract_domain(url)
                            
                            # Avoid duplicates
                            if not any(comp['domain'] == domain for comp in self.discovered_companies):
                                self.discovered_companies.append({
                                    'url': url,
                                    'domain': domain,
                                    'source_query': query
                                })
                                print(f"   ✓ Found: {domain}")
                        
                        time.sleep(1)  # Be respectful
                        
                    except Exception as e:
                        print(f"   ❌ Error processing {url}: {e}")
                        continue
                        
            except Exception as e:
                print(f"   ❌ Search error for '{query}': {e}")
                continue
        
        print(f"   📊 Total discovered: {len(self.discovered_companies)} companies")
    
    def research_and_qualify_leads(self):
        """
        Steps 2-3: Research companies and qualify leads
        """
        for company in self.discovered_companies:
            if len(self.qualified_leads) >= self.target_lead_count:
                break
                
            try:
                print(f"   Researching: {company['domain']}")
                
                # Scrape website for information
                website_info = self.scrape_website(company['url'])
                
                # Qualify the company
                if self.qualify_company(website_info, company):
                    # Create qualified lead
                    lead = {
                        'company_name': website_info.get('title', company['domain']),
                        'domain': company['domain'],
                        'website': company['url'],
                        'description': website_info.get('description', ''),
                        'locations_found': website_info.get('locations', []),
                        'estimated_locations': len(website_info.get('locations', [])),
                        'contact_info': website_info.get('contact_info', {}),
                        'qualification_score': self.calculate_qualification_score(website_info),
                        'discovered_at': datetime.now().isoformat()
                    }
                    
                    self.qualified_leads.append(lead)
                    print(f"   ✅ Qualified: {lead['company_name']}")
                else:
                    print(f"   ❌ Not qualified: {company['domain']}")
                    
                time.sleep(2)  # Be respectful to websites
                
            except Exception as e:
                print(f"   ❌ Error researching {company['domain']}: {e}")
                continue
    
    def scrape_website(self, url: str) -> Dict:
        """
        Scrape website for company information
        """
        try:
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            return {
                'title': self.extract_title(soup),
                'description': self.extract_description(soup),
                'locations': self.extract_locations(soup),
                'contact_info': self.extract_contact_info(soup),
                'about_text': self.extract_about_text(soup)
            }
        except Exception as e:
            print(f"   ⚠️ Scraping error for {url}: {e}")
            return {}
    
    def extract_title(self, soup: BeautifulSoup) -> str:
        """Extract page title"""
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.text.strip()
        return ""
    
    def extract_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            return meta_desc.get('content', '').strip()
        return ""
    
    def extract_locations(self, soup: BeautifulSoup) -> List[str]:
        """Extract location information"""
        locations = []
        text_content = soup.get_text().lower()
        
        # Look for location indicators
        location_patterns = [
            r'(\d+)\s+locations?',
            r'(\d+)\s+offices?',
            r'(\d+)\s+clinics?',
            r'(\d+)\s+practices?'
        ]
        
        for pattern in location_patterns:
            matches = re.findall(pattern, text_content)
            for match in matches:
                try:
                    count = int(match)
                    if count >= self.min_locations:
                        locations.append(f"{count} locations")
                except:
                    continue
        
        # Look for state names
        states = ['california', 'texas', 'florida', 'new york', 'illinois', 'pennsylvania']
        found_states = [state for state in states if state in text_content]
        
        return locations + found_states
    
    def extract_contact_info(self, soup: BeautifulSoup) -> Dict:
        """Extract contact information"""
        text = soup.get_text()
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        
        # Extract phone numbers
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        phones = re.findall(phone_pattern, text)
        
        return {
            'emails': list(set(emails))[:3],  # Limit to 3
            'phones': list(set(phones))[:3]   # Limit to 3
        }
    
    def extract_about_text(self, soup: BeautifulSoup) -> str:
        """Extract about/company information"""
        about_selectors = [
            'div[class*="about"]',
            'section[class*="about"]',
            'div[id*="about"]'
        ]
        
        for selector in about_selectors:
            elements = soup.select(selector)
            if elements:
                return elements[0].get_text().strip()[:300]
        
        return ""
    
    def qualify_company(self, website_info: Dict, company: Dict) -> bool:
        """
        Qualify company based on criteria
        """
        text_content = (
            website_info.get('title', '') + ' ' +
            website_info.get('description', '') + ' ' +
            website_info.get('about_text', '')
        ).lower()
        
        # Check for dental keywords
        dental_keywords = ['dental', 'dentist', 'orthodontic', 'oral']
        has_dental = any(keyword in text_content for keyword in dental_keywords)
        
        # Check for multiple locations
        locations = website_info.get('locations', [])
        has_multiple_locations = len(locations) > 0
        
        # Check for location count in text
        location_numbers = re.findall(r'(\d+)\s+(?:locations?|offices?|clinics?)', text_content)
        has_sufficient_locations = any(int(num) >= self.min_locations for num in location_numbers if num.isdigit())
        
        return has_dental and (has_multiple_locations or has_sufficient_locations)
    
    def calculate_qualification_score(self, website_info: Dict) -> int:
        """
        Calculate qualification score (1-100)
        """
        score = 0
        
        # Base score for being dental
        text_content = (website_info.get('title', '') + ' ' + website_info.get('description', '')).lower()
        if any(keyword in text_content for keyword in ['dental', 'dentist']):
            score += 40
        
        # Location score
        locations = website_info.get('locations', [])
        score += min(len(locations) * 10, 30)
        
        # Contact info score
        contact_info = website_info.get('contact_info', {})
        if contact_info.get('emails'):
            score += 15
        if contact_info.get('phones'):
            score += 15
        
        return min(score, 100)
    
    def simulate_hubspot_integration(self):
        """
        Step 4: Simulate HubSpot integration
        """
        for lead in self.qualified_leads:
            print(f"   📊 [SIMULATED] Creating HubSpot record for: {lead['company_name']}")
            lead['hubspot_simulation'] = {
                'company_id': f"sim_{hash(lead['domain']) % 10000}",
                'contacts_created': len(lead['contact_info'].get('emails', [])),
                'created_at': datetime.now().isoformat()
            }
    
    def generate_demo_emails(self):
        """
        Step 5: Generate demo emails
        """
        for lead in self.qualified_leads:
            email_content = self.create_demo_email(lead)
            lead['demo_email'] = email_content
            print(f"   ✉️ Generated email for: {lead['company_name']}")
    
    def create_demo_email(self, lead: Dict) -> str:
        """
        Create a demo email for the lead
        """
        company_name = lead['company_name']
        locations = lead['estimated_locations']
        
        email_template = f"""
Subject: Streamline Operations Across Your {locations}+ Dental Locations

Hi there,

I noticed {company_name} operates multiple dental locations - that's impressive growth! 

Managing patient communication and scheduling across {locations}+ locations can be challenging. VoiceCare AI helps dental groups like yours automate:

• Patient appointment reminders
• Follow-up communications  
• Insurance verification calls
• Post-treatment check-ins

This frees up your staff to focus on patient care while ensuring consistent communication across all locations.

Would you be interested in a brief 15-minute demo to see how this could work for {company_name}?

Best regards,
VoiceCare AI Team

P.S. We've helped similar multi-location dental groups reduce no-shows by 40% and improve patient satisfaction scores.
        """.strip()
        
        return email_template
    
    def generate_final_report(self):
        """
        Step 6: Generate final report
        """
        report = {
            'summary': {
                'total_discovered': len(self.discovered_companies),
                'qualified_leads': len(self.qualified_leads),
                'success_rate': f"{(len(self.qualified_leads) / max(len(self.discovered_companies), 1) * 100):.1f}%",
                'generated_at': datetime.now().isoformat()
            },
            'leads': self.qualified_leads
        }
        
        # Save to file
        filename = f"demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"   📄 Report saved to: {filename}")
        
        # Print summary
        print(f"\n📊 DEMO RESULTS SUMMARY:")
        print(f"   • Companies Discovered: {report['summary']['total_discovered']}")
        print(f"   • Qualified Leads: {report['summary']['qualified_leads']}")
        print(f"   • Success Rate: {report['summary']['success_rate']}")
        print(f"   • Average Qualification Score: {sum(lead['qualification_score'] for lead in self.qualified_leads) / len(self.qualified_leads):.1f}" if self.qualified_leads else "   • Average Qualification Score: N/A")
    
    def is_potential_dental_company(self, url: str) -> bool:
        """
        Quick check if URL might be a dental company
        """
        url_lower = url.lower()
        dental_indicators = ['dental', 'dentist', 'orthodontic', 'oral', 'smile']
        return any(indicator in url_lower for indicator in dental_indicators)
    
    def extract_domain(self, url: str) -> str:
        """
        Extract domain from URL
        """
        try:
            parsed = urlparse(url)
            return parsed.netloc.replace('www.', '')
        except:
            return url

if __name__ == "__main__":
    print("🤖 VoiceCare AI - Demo Discovery Lead Agent")
    print("Using FREE Google search - No paid APIs required!")
    print("=" * 60)
    
    agent = DemoDiscoveryAgent()
    leads = agent.run_discovery_process()
    
    if leads:
        print(f"\n🎉 SUCCESS! Found {len(leads)} qualified dental organization leads.")
        print("\nSample Lead:")
        if leads:
            sample = leads[0]
            print(f"   Company: {sample['company_name']}")
            print(f"   Domain: {sample['domain']}")
            print(f"   Locations: {sample['estimated_locations']}")
            print(f"   Score: {sample['qualification_score']}/100")
    else:
        print("\n⚠️ No qualified leads found. This could be due to:")
        print("   • Network connectivity issues")
        print("   • Google rate limiting")
        print("   • Strict qualification criteria")
        print("   • Try running again in a few minutes")
