# Discovery Lead Agent Setup Guide

## 🚀 Quick Start Guide

### Prerequisites
1. Python 3.8+ installed
2. API accounts created (see below)
3. Basic understanding of environment variables

### Step 1: API Account Setup

#### 1.1 HubSpot Marketing Hub (Free)
1. Go to [HubSpot](https://app.hubspot.com/signup)
2. Create a free account
3. Navigate to Settings → Integrations → API Key
4. Generate and copy your API key

#### 1.2 Apollo Account (Free)
1. Visit [Apollo.io](https://app.apollo.io/sign-up)
2. Create a free account
3. Go to Settings → Integrations → API
4. Generate and copy your API key

#### 1.3 OpenAI API
1. Go to [OpenAI Platform](https://platform.openai.com/signup)
2. Create account and add payment method
3. Navigate to API Keys section
4. Create new secret key

#### 1.4 Google Search API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Custom Search API
4. Create credentials (API Key)
5. Set up Custom Search Engine at [CSE](https://cse.google.com/)

### Step 2: Installation

```bash
# Clone or download the project
cd discovery_lead_agent

# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.example .env
```

### Step 3: Configuration

Edit your `.env` file:
```env
# Required API Keys
OPENAI_API_KEY=sk-your-openai-key-here
HUBSPOT_API_KEY=your-hubspot-key-here
APOLLO_API_KEY=your-apollo-key-here
GOOGLE_API_KEY=your-google-key-here
GOOGLE_CSE_ID=your-custom-search-engine-id

# Optional Configuration
TARGET_LEAD_COUNT=10
MIN_LOCATIONS=3
GMAIL_USERNAME=<EMAIL>
GMAIL_PASSWORD=your-app-password
```

### Step 4: Run the Agent

```bash
python main.py
```

## 🔧 Detailed Setup Instructions

### HubSpot Setup Details

1. **Create Free Account**
   - Go to HubSpot and sign up for Marketing Hub (Free)
   - Complete the onboarding process
   - Verify your email address

2. **Generate API Key**
   - Click on Settings (gear icon) in top navigation
   - Go to Integrations → API Key
   - Click "Create key" or "Show" if one exists
   - Copy the key to your .env file

3. **Configure Properties (Optional)**
   - Go to Settings → Properties
   - Add custom properties for tracking:
     - `custom_locations_count` (Number)
     - `custom_states_served` (Single-line text)
     - `lead_source` (Single-line text)

### Apollo Setup Details

1. **Create Account**
   - Sign up at Apollo.io with business email
   - Choose the free plan (includes 50 credits/month)
   - Complete profile setup

2. **Get API Key**
   - Go to Settings → Integrations
   - Find "API" section
   - Copy your API key
   - Note: Free plan has rate limits

3. **Install Chrome Extension (Optional)**
   - Install Apollo Chrome extension
   - This helps with manual verification if needed

### Google Search API Setup

1. **Google Cloud Project**
   - Create project in Google Cloud Console
   - Enable Custom Search JSON API
   - Create API key with restrictions

2. **Custom Search Engine**
   - Go to Google Custom Search Engine
   - Create new search engine
   - Set to search entire web
   - Copy the Search Engine ID

### Troubleshooting

#### Common Issues

**1. API Rate Limits**
```
Error: Rate limit exceeded
Solution: Add delays between API calls or upgrade API plan
```

**2. Invalid API Keys**
```
Error: 401 Unauthorized
Solution: Double-check API keys in .env file
```

**3. No Search Results**
```
Error: No companies found
Solution: Adjust search queries in config.py
```

**4. HubSpot Duplicates**
```
Warning: Company already exists
Solution: This is normal - agent prevents duplicates
```

#### Debug Mode

Enable verbose logging:
```python
# In main.py, add:
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Testing the Setup

Run a quick test:
```bash
python -c "
from tools.hubspot_tools import HubSpotTools
from tools.apollo_tools import ApolloTools
print('HubSpot:', 'OK' if HubSpotTools().headers else 'FAIL')
print('Apollo:', 'OK' if ApolloTools().headers else 'FAIL')
"
```

## 📊 Expected Results

After successful setup and execution:

1. **Console Output**: Real-time progress updates
2. **HubSpot Records**: New companies and contacts created
3. **JSON Report**: Detailed results file generated
4. **Email Drafts**: Personalized emails ready for review

### Sample Success Output
```
🚀 Starting Discovery Lead Agent Process...
📍 Step 1: Discovering potential leads...
Searching: dental service organization DSO multiple locations
✅ brightdental.com - Qualified lead with 5 contacts
✅ smiledental.com - Qualified lead with 3 contacts
📊 Step 4: Creating HubSpot records for 10 leads...
✅ Created company: Bright Dental Group
✅ Created contact: John Smith
✉️ Step 5: Generating emails for 10 leads...
✅ Email draft saved for John Smith
🎉 SUCCESS! Discovered 10 qualified leads.
```

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit .env file to version control
2. **API Keys**: Use least-privilege access when possible
3. **Rate Limiting**: Respect API rate limits to avoid blocks
4. **Data Handling**: Follow GDPR/privacy guidelines for contact data

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all API keys are correct and active
3. Ensure you have sufficient API credits/quotas
4. Review the console output for specific error messages

For additional help, refer to the main README.md file or create an issue in the repository.
