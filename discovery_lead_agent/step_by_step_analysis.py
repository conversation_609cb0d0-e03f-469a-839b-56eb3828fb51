"""
Step-by-Step Analysis of Discovery Lead Agent Results
This script analyzes each step of the agent process and shows detailed outputs
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np
from collections import Counter
import re

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_step(step_num, title):
    """Print a formatted step header"""
    print(f"\n📍 STEP {step_num}: {title}")
    print("-" * 50)

def load_and_examine_data():
    """Step 1: Load and examine the results data"""
    print_step(1, "LOAD AND EXAMINE DATA")
    
    # Load the results
    with open('demo_results_20250712_195756.json', 'r') as f:
        results = json.load(f)
    
    print("📊 DISCOVERY AGENT RESULTS OVERVIEW:")
    print(f"   📅 Generated at: {results['summary']['generated_at']}")
    print(f"   🏢 Total companies discovered: {results['summary']['total_discovered']}")
    print(f"   ✅ Qualified leads: {results['summary']['qualified_leads']}")
    print(f"   📈 Success rate: {results['summary']['success_rate']}")
    print(f"   📋 Detailed lead records: {len(results['leads'])}")
    
    return results

def analyze_discovery_process(results):
    """Step 2: Analyze the lead discovery process"""
    print_step(2, "ANALYZE DISCOVERY PROCESS")
    
    leads_df = pd.DataFrame(results['leads'])
    
    print("🔍 LEAD DISCOVERY BREAKDOWN:")
    print(f"   Total qualified leads: {len(leads_df)}")
    
    print("\n🏢 DISCOVERED COMPANIES:")
    for i, (company, domain) in enumerate(zip(leads_df['company_name'], leads_df['domain']), 1):
        print(f"   {i}. {company[:50]}...")
        print(f"      Domain: {domain}")
    
    return leads_df

def analyze_qualification_scores(leads_df):
    """Step 3: Analyze qualification scores"""
    print_step(3, "ANALYZE QUALIFICATION SCORES")
    
    scores = leads_df['qualification_score'].values
    
    print("🎯 QUALIFICATION SCORE STATISTICS:")
    print(f"   Average score: {np.mean(scores):.1f}/100")
    print(f"   Median score: {np.median(scores):.1f}/100")
    print(f"   Highest score: {np.max(scores)}/100")
    print(f"   Lowest score: {np.min(scores)}/100")
    print(f"   Standard deviation: {np.std(scores):.1f}")
    
    print("\n📋 DETAILED SCORE BREAKDOWN:")
    for i, (company, score) in enumerate(zip(leads_df['company_name'], scores), 1):
        print(f"   {i}. {company[:40]}... → {score}/100")
    
    # Create visualization
    plt.figure(figsize=(12, 5))
    
    # Score distribution
    plt.subplot(1, 2, 1)
    bars = plt.bar(range(len(scores)), scores, color='skyblue', alpha=0.7)
    plt.axhline(y=np.mean(scores), color='red', linestyle='--', 
                label=f'Average: {np.mean(scores):.1f}')
    plt.xlabel('Lead Index')
    plt.ylabel('Qualification Score')
    plt.title('Qualification Scores by Lead')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom')
    
    # Score histogram
    plt.subplot(1, 2, 2)
    plt.hist(scores, bins=5, color='lightgreen', alpha=0.7, edgecolor='black')
    plt.xlabel('Qualification Score')
    plt.ylabel('Number of Leads')
    plt.title('Distribution of Qualification Scores')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('qualification_scores_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return scores

def analyze_location_data(leads_df):
    """Step 4: Analyze location information"""
    print_step(4, "ANALYZE LOCATION DATA")
    
    location_counts = leads_df['estimated_locations'].values
    
    print("📍 LOCATION STATISTICS:")
    print(f"   Average locations per company: {np.mean(location_counts):.1f}")
    print(f"   Total locations across all leads: {np.sum(location_counts)}")
    print(f"   Company with most locations: {np.max(location_counts)}")
    print(f"   Company with least locations: {np.min(location_counts)}")
    
    print("\n🏢 LOCATION BREAKDOWN BY COMPANY:")
    for i, (company, locations, found_locs) in enumerate(zip(
        leads_df['company_name'], 
        leads_df['estimated_locations'],
        leads_df['locations_found']
    ), 1):
        print(f"\n   {i}. {company[:40]}...")
        print(f"      Estimated locations: {locations}")
        print(f"      Location indicators: {found_locs[:3]}")  # Show first 3
    
    # Visualize location distribution
    plt.figure(figsize=(12, 6))
    companies_short = [name[:25] + '...' if len(name) > 25 else name 
                      for name in leads_df['company_name']]
    bars = plt.barh(companies_short, location_counts, color='orange', alpha=0.7)
    plt.xlabel('Number of Locations')
    plt.title('Estimated Locations per Company')
    plt.grid(True, alpha=0.3)
    
    # Add value labels
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                f'{int(width)}', ha='left', va='center')
    
    plt.tight_layout()
    plt.savefig('location_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return location_counts

def analyze_contact_information(leads_df):
    """Step 5: Analyze contact information"""
    print_step(5, "ANALYZE CONTACT INFORMATION")
    
    total_emails = 0
    total_phones = 0
    companies_with_emails = 0
    companies_with_phones = 0
    
    contact_details = []
    
    print("📞 CONTACT INFORMATION BREAKDOWN:")
    for i, (company, contact_info) in enumerate(zip(leads_df['company_name'], leads_df['contact_info']), 1):
        emails = contact_info.get('emails', [])
        phones = contact_info.get('phones', [])
        
        total_emails += len(emails)
        total_phones += len(phones)
        
        if emails:
            companies_with_emails += 1
        if phones:
            companies_with_phones += 1
        
        contact_details.append({
            'company': company,
            'email_count': len(emails),
            'phone_count': len(phones),
            'emails': emails,
            'phones': phones
        })
        
        print(f"\n   {i}. {company[:40]}...")
        print(f"      📧 Emails found: {len(emails)}")
        for email in emails:
            print(f"         • {email}")
        print(f"      📱 Phones found: {len(phones)}")
        for phone in phones:
            print(f"         • {phone}")
    
    print(f"\n📊 CONTACT SUMMARY:")
    print(f"   Total emails found: {total_emails}")
    print(f"   Total phones found: {total_phones}")
    print(f"   Companies with emails: {companies_with_emails}/{len(leads_df)} ({companies_with_emails/len(leads_df)*100:.1f}%)")
    print(f"   Companies with phones: {companies_with_phones}/{len(leads_df)} ({companies_with_phones/len(leads_df)*100:.1f}%)")
    
    # Visualize contact information
    contact_df = pd.DataFrame(contact_details)
    
    plt.figure(figsize=(14, 6))
    
    plt.subplot(1, 2, 1)
    companies_short = [name[:20] + '...' if len(name) > 20 else name 
                      for name in contact_df['company']]
    x_pos = np.arange(len(companies_short))
    
    plt.bar(x_pos, contact_df['email_count'], color='lightblue', alpha=0.7, label='Emails')
    plt.bar(x_pos, contact_df['phone_count'], bottom=contact_df['email_count'], 
            color='lightcoral', alpha=0.7, label='Phones')
    plt.xlabel('Companies')
    plt.ylabel('Contact Count')
    plt.title('Contact Information by Company')
    plt.legend()
    plt.xticks(x_pos, companies_short, rotation=45, ha='right')
    
    plt.subplot(1, 2, 2)
    contact_types = ['Companies\nwith Emails', 'Companies\nwith Phones']
    contact_counts = [companies_with_emails, companies_with_phones]
    colors = ['lightblue', 'lightcoral']
    plt.pie(contact_counts, labels=contact_types, autopct='%1.1f%%', 
            startangle=90, colors=colors)
    plt.title('Contact Information Coverage')
    
    plt.tight_layout()
    plt.savefig('contact_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return contact_details

def analyze_generated_emails(leads_df):
    """Step 6: Analyze generated emails"""
    print_step(6, "ANALYZE GENERATED EMAILS")
    
    email_stats = []
    
    print("✉️ EMAIL GENERATION ANALYSIS:")
    for i, (company, email) in enumerate(zip(leads_df['company_name'], leads_df['demo_email']), 1):
        # Extract subject line
        subject_match = re.search(r'Subject: (.+?)\\n', email)
        subject = subject_match.group(1) if subject_match else "No subject found"
        
        # Calculate email metrics
        word_count = len(email.split())
        char_count = len(email)
        line_count = len(email.split('\\n'))
        
        email_stats.append({
            'company': company,
            'subject': subject,
            'word_count': word_count,
            'char_count': char_count,
            'line_count': line_count
        })
        
        print(f"\n   {i}. {company[:40]}...")
        print(f"      📧 Subject: {subject}")
        print(f"      📝 Word count: {word_count}")
        print(f"      📏 Character count: {char_count}")
    
    # Email statistics
    email_df = pd.DataFrame(email_stats)
    avg_words = email_df['word_count'].mean()
    avg_chars = email_df['char_count'].mean()
    
    print(f"\n📊 EMAIL STATISTICS:")
    print(f"   Average word count: {avg_words:.1f}")
    print(f"   Average character count: {avg_chars:.1f}")
    print(f"   Total emails generated: {len(email_df)}")
    
    return email_stats

def show_sample_email(leads_df):
    """Step 7: Show sample email"""
    print_step(7, "SAMPLE GENERATED EMAIL")
    
    # Get the highest scoring lead's email
    best_lead_idx = leads_df['qualification_score'].idxmax()
    best_company = leads_df.loc[best_lead_idx, 'company_name']
    best_email = leads_df.loc[best_lead_idx, 'demo_email']
    best_score = leads_df.loc[best_lead_idx, 'qualification_score']
    
    print(f"🏆 BEST QUALIFIED LEAD:")
    print(f"   Company: {best_company}")
    print(f"   Qualification Score: {best_score}/100")
    print("\n" + "="*80)
    print("📧 GENERATED EMAIL:")
    print("="*80)
    print(best_email.replace('\\n', '\n'))
    print("="*80)

def analyze_hubspot_integration(leads_df):
    """Step 8: Analyze HubSpot integration"""
    print_step(8, "ANALYZE HUBSPOT INTEGRATION")
    
    hubspot_data = []
    total_contacts_created = 0
    
    print("📊 HUBSPOT INTEGRATION ANALYSIS:")
    for i, (company, hubspot_sim) in enumerate(zip(leads_df['company_name'], leads_df['hubspot_simulation']), 1):
        company_id = hubspot_sim.get('company_id', 'N/A')
        contacts_created = hubspot_sim.get('contacts_created', 0)
        created_at = hubspot_sim.get('created_at', 'N/A')
        
        total_contacts_created += contacts_created
        
        hubspot_data.append({
            'company': company,
            'company_id': company_id,
            'contacts_created': contacts_created,
            'created_at': created_at
        })
        
        print(f"\n   {i}. {company[:40]}...")
        print(f"      🆔 HubSpot Company ID: {company_id}")
        print(f"      👥 Contacts Created: {contacts_created}")
        print(f"      📅 Created At: {created_at[:19]}")
    
    print(f"\n📊 HUBSPOT SUMMARY:")
    print(f"   Total companies created: {len(hubspot_data)}")
    print(f"   Total contacts created: {total_contacts_created}")
    print(f"   Average contacts per company: {total_contacts_created/len(hubspot_data):.1f}")
    
    return hubspot_data

def generate_final_summary(results, leads_df, scores, location_counts, contact_details, hubspot_data):
    """Step 9: Generate final performance summary"""
    print_step(9, "FINAL PERFORMANCE SUMMARY")
    
    # Calculate key metrics
    total_discovered = results['summary']['total_discovered']
    qualified_leads = results['summary']['qualified_leads']
    success_rate = (qualified_leads / total_discovered) * 100
    
    avg_score = np.mean(scores)
    avg_locations = np.mean(location_counts)
    total_locations = np.sum(location_counts)
    
    companies_with_contact_info = len([1 for contact in contact_details 
                                     if contact['emails'] or contact['phones']])
    
    total_contacts_created = sum(h['contacts_created'] for h in hubspot_data)
    
    print("🎯 COMPREHENSIVE PERFORMANCE METRICS:")
    print(f"\n📊 DISCOVERY METRICS:")
    print(f"   • Companies Discovered: {total_discovered}")
    print(f"   • Qualified Leads: {qualified_leads}")
    print(f"   • Success Rate: {success_rate:.1f}%")
    print(f"   • Average Qualification Score: {avg_score:.1f}/100")
    
    print(f"\n🏢 COMPANY METRICS:")
    print(f"   • Average Locations per Company: {avg_locations:.1f}")
    print(f"   • Total Locations Across All Leads: {total_locations}")
    print(f"   • Companies with Contact Info: {companies_with_contact_info}/{qualified_leads} ({companies_with_contact_info/qualified_leads*100:.1f}%)")
    
    print(f"\n✉️ EMAIL METRICS:")
    print(f"   • Emails Generated: {len(leads_df)}")
    print(f"   • Email Generation Success Rate: 100%")
    
    print(f"\n📊 HUBSPOT METRICS:")
    print(f"   • Company Records Created: {len(hubspot_data)}")
    print(f"   • Contact Records Created: {total_contacts_created}")
    print(f"   • Integration Success Rate: 100%")
    
    print(f"\n🎉 OVERALL ASSESSMENT:")
    print(f"   ✅ All 6 steps completed successfully")
    print(f"   ✅ High-quality leads identified")
    print(f"   ✅ Comprehensive contact information extracted")
    print(f"   ✅ Personalized emails generated")
    print(f"   ✅ Ready for real API integration")

def main():
    """Main analysis function"""
    print_header("DISCOVERY LEAD AGENT - STEP-BY-STEP ANALYSIS")
    print(f"Analysis started at: {datetime.now()}")
    
    # Step 1: Load data
    results = load_and_examine_data()
    
    # Step 2: Analyze discovery
    leads_df = analyze_discovery_process(results)
    
    # Step 3: Analyze scores
    scores = analyze_qualification_scores(leads_df)
    
    # Step 4: Analyze locations
    location_counts = analyze_location_data(leads_df)
    
    # Step 5: Analyze contacts
    contact_details = analyze_contact_information(leads_df)
    
    # Step 6: Analyze emails
    email_stats = analyze_generated_emails(leads_df)
    
    # Step 7: Show sample email
    show_sample_email(leads_df)
    
    # Step 8: Analyze HubSpot
    hubspot_data = analyze_hubspot_integration(leads_df)
    
    # Step 9: Final summary
    generate_final_summary(results, leads_df, scores, location_counts, contact_details, hubspot_data)
    
    print_header("ANALYSIS COMPLETE!")
    print("📊 Charts saved as PNG files in current directory")
    print("🎯 The Discovery Lead Agent successfully demonstrated all capabilities!")

if __name__ == "__main__":
    # Set up plotting
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Run analysis
    main()
