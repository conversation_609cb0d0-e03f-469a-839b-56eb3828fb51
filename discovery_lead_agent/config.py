"""
Configuration settings for the Discovery Lead Agent
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # API Keys (Optional - will work without them in demo mode)
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "demo-mode")
    HUBSPOT_API_KEY = os.getenv("HUBSPOT_API_KEY", "demo-mode")
    APOLLO_API_KEY = os.getenv("APOLLO_API_KEY", "demo-mode")
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "demo-mode")
    GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID", "demo-mode")
    
    # LinkedIn Credentials
    LINKEDIN_USERNAME = os.getenv("LINKEDIN_USERNAME")
    LINKEDIN_PASSWORD = os.getenv("LINKEDIN_PASSWORD")
    
    # Email Configuration
    GMAIL_USERNAME = os.getenv("GMAIL_USERNAME")
    GMAIL_PASSWORD = os.getenv("GMAIL_PASSWORD")
    
    # Agent Settings
    TARGET_LEAD_COUNT = int(os.getenv("TARGET_LEAD_COUNT", 10))
    MIN_LOCATIONS = int(os.getenv("MIN_LOCATIONS", 3))
    
    # Search Queries for Lead Discovery
    SEARCH_QUERIES = [
        "dental service organization DSO multiple locations",
        "dental group practice multiple offices",
        "multi-location dental practice",
        "dental management company multiple clinics",
        "dental chain multiple locations"
    ]
    
    # Target Contact Titles
    TARGET_TITLES = [
        "CEO", "COO", "CFO", "CIO", "CTO", "CMO",
        "VP", "Vice President", "Director",
        "Operations", "IT", "Admin", "Revenue Cycle", 
        "Managed Care", "Finance"
    ]
    
    # Excluded Titles
    EXCLUDED_TITLES = [
        "Manager", "Coordinator", "Specialist", "Assistant",
        "Analyst", "Associate", "Representative"
    ]
    
    # Company Validation Criteria
    VALIDATION_CRITERIA = {
        "min_locations": MIN_LOCATIONS,
        "required_keywords": ["dental", "dentist", "oral", "orthodontic"],
        "excluded_keywords": ["supplier", "vendor", "equipment", "laboratory"]
    }
