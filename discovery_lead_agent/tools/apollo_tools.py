"""
Apollo integration tools for contact enrichment
"""
import requests
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config
from models import Contact

class ApolloTools:
    def __init__(self):
        self.config = Config()
        self.base_url = "https://api.apollo.io/v1"
        self.headers = {
            "Cache-Control": "no-cache",
            "Content-Type": "application/json",
            "X-Api-Key": self.config.APOLLO_API_KEY
        }
    
    def search_people_by_domain(self, domain: str) -> List[Contact]:
        """
        Search for people at a specific domain using Apollo API
        """
        try:
            url = f"{self.base_url}/mixed_people/search"
            
            # Build search criteria based on target titles
            title_keywords = []
            for title in self.config.TARGET_TITLES:
                title_keywords.append(title.lower())
            
            data = {
                "q_organization_domains": domain,
                "person_titles": title_keywords,
                "page": 1,
                "per_page": 25,
                "organization_locations": [],
                "person_locations": []
            }
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 200:
                results = response.json()
                contacts = []
                
                for person in results.get("people", []):
                    if self.is_valid_contact(person):
                        contact = self.parse_apollo_contact(person)
                        if contact:
                            contacts.append(contact)
                
                return contacts
            else:
                print(f"Apollo search error: {response.text}")
                return []
                
        except Exception as e:
            print(f"Apollo API error: {e}")
            return []
    
    def enrich_contact(self, email: str) -> Optional[Contact]:
        """
        Enrich contact information using Apollo API
        """
        try:
            url = f"{self.base_url}/people/match"
            
            data = {
                "email": email
            }
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 200:
                person_data = response.json()
                if person_data.get("person"):
                    return self.parse_apollo_contact(person_data["person"])
            
            return None
            
        except Exception as e:
            print(f"Apollo enrichment error: {e}")
            return None
    
    def parse_apollo_contact(self, person_data: Dict) -> Optional[Contact]:
        """
        Parse Apollo API response into Contact model
        """
        try:
            name = f"{person_data.get('first_name', '')} {person_data.get('last_name', '')}".strip()
            title = person_data.get('title', '')
            email = person_data.get('email', '')
            linkedin_url = person_data.get('linkedin_url', '')
            
            # Determine department from title
            department = self.determine_department(title)
            
            # Verify email if available
            verified = bool(email and person_data.get('email_status') == 'verified')
            
            if name and title:
                return Contact(
                    name=name,
                    title=title,
                    email=email,
                    linkedin_url=linkedin_url,
                    department=department,
                    verified=verified
                )
            
            return None
            
        except Exception as e:
            print(f"Contact parsing error: {e}")
            return None
    
    def is_valid_contact(self, person_data: Dict) -> bool:
        """
        Validate if the contact meets our criteria
        """
        title = person_data.get('title', '').lower()
        
        # Check if title contains target keywords
        has_target_title = any(target.lower() in title for target in self.config.TARGET_TITLES)
        
        # Check if title contains excluded keywords
        has_excluded_title = any(excluded.lower() in title for excluded in self.config.EXCLUDED_TITLES)
        
        # Must have verified email
        has_verified_email = person_data.get('email_status') == 'verified'
        
        return has_target_title and not has_excluded_title and has_verified_email
    
    def determine_department(self, title: str) -> str:
        """
        Determine department based on job title
        """
        title_lower = title.lower()
        
        department_mapping = {
            'operations': ['operations', 'ops', 'operational'],
            'it': ['it', 'information technology', 'tech', 'cio', 'cto'],
            'admin': ['admin', 'administrative', 'office'],
            'revenue cycle': ['revenue', 'billing', 'rcm', 'cycle'],
            'managed care': ['managed care', 'insurance', 'payer'],
            'finance': ['finance', 'financial', 'cfo', 'accounting'],
            'executive': ['ceo', 'president', 'executive', 'chief']
        }
        
        for dept, keywords in department_mapping.items():
            if any(keyword in title_lower for keyword in keywords):
                return dept.title()
        
        return 'Other'
    
    def bulk_enrich_contacts(self, contacts: List[Contact]) -> List[Contact]:
        """
        Bulk enrich multiple contacts
        """
        enriched_contacts = []
        
        for contact in contacts:
            if contact.email:
                enriched = self.enrich_contact(contact.email)
                if enriched:
                    enriched_contacts.append(enriched)
                else:
                    enriched_contacts.append(contact)
            else:
                enriched_contacts.append(contact)
        
        return enriched_contacts
