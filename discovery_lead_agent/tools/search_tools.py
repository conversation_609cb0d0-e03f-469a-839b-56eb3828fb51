"""
Search tools for lead discovery using free Google search
"""
import requests
from bs4 import BeautifulSoup
from googlesearch import search
from typing import List, Dict
import time
import re
import sys
import os
from urllib.parse import urlparse
from fake_useragent import UserAgent
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config

class SearchTools:
    def __init__(self):
        self.config = Config()
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random
        })
    
    def google_search(self, query: str, num_results: int = 10) -> List[Dict]:
        """
        Perform free Google search for dental organizations
        """
        try:
            print(f"Searching Google for: {query}")
            search_results = []

            # Use free googlesearch library
            urls = search(query, num_results=num_results, sleep_interval=2, lang='en')

            for url in urls:
                try:
                    # Get page title and snippet
                    response = self.session.get(url, timeout=10)
                    soup = BeautifulSoup(response.content, 'html.parser')

                    title = soup.find('title').text if soup.find('title') else url

                    # Extract meta description as snippet
                    meta_desc = soup.find('meta', attrs={'name': 'description'})
                    snippet = meta_desc.get('content', '') if meta_desc else ''

                    # If no meta description, get first paragraph
                    if not snippet:
                        first_p = soup.find('p')
                        snippet = first_p.text[:200] if first_p else ''

                    search_results.append({
                        'title': title.strip(),
                        'link': url,
                        'snippet': snippet.strip(),
                        'domain': self.extract_domain(url)
                    })

                    time.sleep(1)  # Be respectful to websites

                except Exception as e:
                    print(f"Error processing {url}: {e}")
                    continue

            print(f"Found {len(search_results)} results")
            return search_results

        except Exception as e:
            print(f"Google search error: {e}")
            return []
    
    def extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.replace('www.', '')
        except:
            return ""
    
    def scrape_website(self, url: str) -> Dict:
        """
        Scrape website for company information using requests
        """
        try:
            print(f"Scraping website: {url}")
            response = self.session.get(url, timeout=15)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract company information
            company_info = {
                'title': soup.find('title').text if soup.find('title') else '',
                'description': self.extract_description(soup),
                'locations': self.extract_locations(soup),
                'contact_info': self.extract_contact_info(soup),
                'about_text': self.extract_about_text(soup)
            }

            return company_info
        except Exception as e:
            print(f"Website scraping error for {url}: {e}")
            return {}
    
    def extract_description(self, soup: BeautifulSoup) -> str:
        """Extract company description from meta tags"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            return meta_desc.get('content', '')
        return ""
    
    def extract_locations(self, soup: BeautifulSoup) -> List[str]:
        """Extract location information from website"""
        locations = []
        
        # Look for common location indicators
        location_keywords = ['location', 'office', 'clinic', 'practice', 'branch']
        
        for keyword in location_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for element in elements:
                # Extract potential location names
                parent = element.parent if element.parent else element
                text = parent.get_text().strip()
                if len(text) < 200:  # Avoid long paragraphs
                    locations.append(text)
        
        return list(set(locations))[:10]  # Limit to 10 unique locations
    
    def extract_contact_info(self, soup: BeautifulSoup) -> Dict:
        """Extract contact information from website"""
        contact_info = {
            'emails': [],
            'phones': [],
            'addresses': []
        }
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, soup.get_text())
        contact_info['emails'] = list(set(emails))
        
        # Extract phone numbers
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        phones = re.findall(phone_pattern, soup.get_text())
        contact_info['phones'] = list(set(phones))
        
        return contact_info
    
    def extract_about_text(self, soup: BeautifulSoup) -> str:
        """Extract about/company information"""
        about_selectors = [
            'div[class*="about"]',
            'section[class*="about"]',
            'div[id*="about"]',
            'p[class*="description"]'
        ]
        
        for selector in about_selectors:
            elements = soup.select(selector)
            if elements:
                return elements[0].get_text().strip()[:500]  # Limit to 500 chars
        
        return ""
    
    def validate_dental_company(self, company_info: Dict) -> bool:
        """
        Validate if the company is a dental organization with multiple locations
        """
        text_content = (
            company_info.get('title', '') + ' ' +
            company_info.get('description', '') + ' ' +
            company_info.get('about_text', '')
        ).lower()
        
        # Check for dental keywords
        dental_keywords = self.config.VALIDATION_CRITERIA['required_keywords']
        has_dental_keywords = any(keyword in text_content for keyword in dental_keywords)
        
        # Check for excluded keywords
        excluded_keywords = self.config.VALIDATION_CRITERIA['excluded_keywords']
        has_excluded_keywords = any(keyword in text_content for keyword in excluded_keywords)
        
        # Check location count (rough estimate)
        location_count = len(company_info.get('locations', []))
        has_multiple_locations = location_count >= self.config.VALIDATION_CRITERIA['min_locations']
        
        return has_dental_keywords and not has_excluded_keywords and has_multiple_locations
    
    def close_session(self):
        """Close the requests session"""
        if hasattr(self, 'session'):
            self.session.close()
