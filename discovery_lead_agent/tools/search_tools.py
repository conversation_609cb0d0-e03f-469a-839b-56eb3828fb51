"""
Search tools for lead discovery
"""
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from typing import List, Dict
import time
import re
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config

class SearchTools:
    def __init__(self):
        self.config = Config()
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Chrome driver for web scraping"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        self.driver = webdriver.Chrome(
            ChromeDriverManager().install(), 
            options=chrome_options
        )
    
    def google_search(self, query: str, num_results: int = 10) -> List[Dict]:
        """
        Perform Google search for dental organizations
        """
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.config.GOOGLE_API_KEY,
                'cx': self.config.GOOGLE_CSE_ID,
                'q': query,
                'num': num_results
            }
            
            response = requests.get(url, params=params)
            results = response.json()
            
            search_results = []
            if 'items' in results:
                for item in results['items']:
                    search_results.append({
                        'title': item.get('title', ''),
                        'link': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'domain': self.extract_domain(item.get('link', ''))
                    })
            
            return search_results
        except Exception as e:
            print(f"Google search error: {e}")
            return []
    
    def extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.replace('www.', '')
        except:
            return ""
    
    def scrape_website(self, url: str) -> Dict:
        """
        Scrape website for company information
        """
        try:
            self.driver.get(url)
            time.sleep(3)
            
            # Get page content
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Extract company information
            company_info = {
                'title': soup.find('title').text if soup.find('title') else '',
                'description': self.extract_description(soup),
                'locations': self.extract_locations(soup),
                'contact_info': self.extract_contact_info(soup),
                'about_text': self.extract_about_text(soup)
            }
            
            return company_info
        except Exception as e:
            print(f"Website scraping error for {url}: {e}")
            return {}
    
    def extract_description(self, soup: BeautifulSoup) -> str:
        """Extract company description from meta tags"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            return meta_desc.get('content', '')
        return ""
    
    def extract_locations(self, soup: BeautifulSoup) -> List[str]:
        """Extract location information from website"""
        locations = []
        
        # Look for common location indicators
        location_keywords = ['location', 'office', 'clinic', 'practice', 'branch']
        
        for keyword in location_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for element in elements:
                # Extract potential location names
                parent = element.parent if element.parent else element
                text = parent.get_text().strip()
                if len(text) < 200:  # Avoid long paragraphs
                    locations.append(text)
        
        return list(set(locations))[:10]  # Limit to 10 unique locations
    
    def extract_contact_info(self, soup: BeautifulSoup) -> Dict:
        """Extract contact information from website"""
        contact_info = {
            'emails': [],
            'phones': [],
            'addresses': []
        }
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, soup.get_text())
        contact_info['emails'] = list(set(emails))
        
        # Extract phone numbers
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        phones = re.findall(phone_pattern, soup.get_text())
        contact_info['phones'] = list(set(phones))
        
        return contact_info
    
    def extract_about_text(self, soup: BeautifulSoup) -> str:
        """Extract about/company information"""
        about_selectors = [
            'div[class*="about"]',
            'section[class*="about"]',
            'div[id*="about"]',
            'p[class*="description"]'
        ]
        
        for selector in about_selectors:
            elements = soup.select(selector)
            if elements:
                return elements[0].get_text().strip()[:500]  # Limit to 500 chars
        
        return ""
    
    def validate_dental_company(self, company_info: Dict) -> bool:
        """
        Validate if the company is a dental organization with multiple locations
        """
        text_content = (
            company_info.get('title', '') + ' ' +
            company_info.get('description', '') + ' ' +
            company_info.get('about_text', '')
        ).lower()
        
        # Check for dental keywords
        dental_keywords = self.config.VALIDATION_CRITERIA['required_keywords']
        has_dental_keywords = any(keyword in text_content for keyword in dental_keywords)
        
        # Check for excluded keywords
        excluded_keywords = self.config.VALIDATION_CRITERIA['excluded_keywords']
        has_excluded_keywords = any(keyword in text_content for keyword in excluded_keywords)
        
        # Check location count (rough estimate)
        location_count = len(company_info.get('locations', []))
        has_multiple_locations = location_count >= self.config.VALIDATION_CRITERIA['min_locations']
        
        return has_dental_keywords and not has_excluded_keywords and has_multiple_locations
    
    def close_driver(self):
        """Close the web driver"""
        if hasattr(self, 'driver'):
            self.driver.quit()
