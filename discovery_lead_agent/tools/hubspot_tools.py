"""
HubSpot integration tools
"""
import requests
import time
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config
from models import Company, Contact, Lead

class HubSpotTools:
    def __init__(self):
        self.config = Config()
        self.base_url = "https://api.hubapi.com"
        self.headers = {
            "Authorization": f"Bearer {self.config.HUBSPOT_API_KEY}",
            "Content-Type": "application/json"
        }
    
    def create_company(self, company: Company) -> Optional[str]:
        """
        Create a new company record in HubSpot
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/companies"
            
            properties = {
                "name": company.consumer_facing_name,
                "domain": company.domain,
                "website": company.website,
                "industry": company.industry,
                "description": company.description or "",
                "num_associated_contacts": len(company.contacts),
                "custom_locations_count": company.locations_count,
                "custom_states_served": ", ".join(company.states_served)
            }
            
            data = {"properties": properties}
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 201:
                company_data = response.json()
                return company_data.get("id")
            else:
                print(f"Error creating company: {response.text}")
                return None
                
        except Exception as e:
            print(f"HubSpot company creation error: {e}")
            return None
    
    def create_contact(self, contact: Contact, company_id: Optional[str] = None) -> Optional[str]:
        """
        Create a new contact record in HubSpot
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/contacts"
            
            properties = {
                "firstname": contact.name.split()[0] if contact.name else "",
                "lastname": " ".join(contact.name.split()[1:]) if len(contact.name.split()) > 1 else "",
                "email": contact.email or "",
                "jobtitle": contact.title,
                "linkedin_url": contact.linkedin_url or "",
                "department": contact.department or ""
            }
            
            data = {"properties": properties}
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 201:
                contact_data = response.json()
                contact_id = contact_data.get("id")
                
                # Associate contact with company if company_id provided
                if company_id and contact_id:
                    self.associate_contact_with_company(contact_id, company_id)
                
                return contact_id
            else:
                print(f"Error creating contact: {response.text}")
                return None
                
        except Exception as e:
            print(f"HubSpot contact creation error: {e}")
            return None
    
    def associate_contact_with_company(self, contact_id: str, company_id: str) -> bool:
        """
        Associate a contact with a company in HubSpot
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/contacts/{contact_id}/associations/companies/{company_id}/1"
            
            response = requests.put(url, headers=self.headers)
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"HubSpot association error: {e}")
            return False
    
    def check_duplicate_company(self, domain: str) -> Optional[str]:
        """
        Check if a company with the same domain already exists
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/companies/search"
            
            data = {
                "filterGroups": [{
                    "filters": [{
                        "propertyName": "domain",
                        "operator": "EQ",
                        "value": domain
                    }]
                }]
            }
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 200:
                results = response.json()
                if results.get("total", 0) > 0:
                    return results["results"][0]["id"]
            
            return None
            
        except Exception as e:
            print(f"HubSpot duplicate check error: {e}")
            return None
    
    def check_duplicate_contact(self, email: str) -> Optional[str]:
        """
        Check if a contact with the same email already exists
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/contacts/search"
            
            data = {
                "filterGroups": [{
                    "filters": [{
                        "propertyName": "email",
                        "operator": "EQ",
                        "value": email
                    }]
                }]
            }
            
            response = requests.post(url, json=data, headers=self.headers)
            
            if response.status_code == 200:
                results = response.json()
                if results.get("total", 0) > 0:
                    return results["results"][0]["id"]
            
            return None
            
        except Exception as e:
            print(f"HubSpot duplicate contact check error: {e}")
            return None
    
    def log_activity(self, object_type: str, object_id: str, activity_type: str, notes: str) -> bool:
        """
        Log activity in HubSpot
        """
        try:
            url = f"{self.base_url}/crm/v3/objects/{object_type}/{object_id}/notes"
            
            data = {
                "properties": {
                    "hs_note_body": f"[{activity_type}] {notes}",
                    "hs_timestamp": int(time.time() * 1000)
                }
            }
            
            response = requests.post(url, json=data, headers=self.headers)
            
            return response.status_code == 201

        except Exception as e:
            print(f"HubSpot activity logging error: {e}")
            return False
