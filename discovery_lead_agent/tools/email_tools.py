"""
Email generation and management tools
"""
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from typing import Dict, List, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config
from models import Contact, Company, Lead

class EmailTools:
    def __init__(self):
        self.config = Config()
        self.llm = OpenAI(
            openai_api_key=self.config.OPENAI_API_KEY,
            temperature=0.7
        )
        self.setup_email_templates()
    
    def setup_email_templates(self):
        """
        Setup email templates for different scenarios
        """
        self.email_prompt = PromptTemplate(
            input_variables=[
                "contact_name", "contact_title", "company_name", 
                "company_description", "locations_count", "states_served",
                "personalization_notes"
            ],
            template="""
            Write a concise, casual, and personalized email for a dental service organization outreach.
            
            Contact Information:
            - Name: {contact_name}
            - Title: {contact_title}
            - Company: {company_name}
            
            Company Details:
            - Description: {company_description}
            - Number of Locations: {locations_count}
            - States Served: {states_served}
            
            Personalization Notes: {personalization_notes}
            
            Email Guidelines:
            1. Keep it under 150 words
            2. Use a casual, friendly tone
            3. Mention their specific role and company
            4. Reference their multi-location presence
            5. Introduce VoiceCare AI as a solution for dental practices
            6. Include a clear call-to-action
            7. Don't be overly salesy
            
            VoiceCare AI Context:
            VoiceCare AI helps dental practices automate patient communication, appointment scheduling, 
            and follow-ups using advanced AI technology. Perfect for multi-location dental organizations 
            looking to streamline operations and improve patient experience.
            
            Write the email with subject line and body:
            """
        )
        
        self.email_chain = LLMChain(llm=self.llm, prompt=self.email_prompt)
    
    def generate_personalized_email(self, lead: Lead) -> str:
        """
        Generate a personalized email for a lead
        """
        try:
            contact = lead.primary_contact or (lead.company.contacts[0] if lead.company.contacts else None)
            
            if not contact:
                return "No primary contact available for email generation"
            
            # Prepare personalization notes
            personalization_notes = self.create_personalization_notes(lead)
            
            # Generate email using LangChain
            email_content = self.email_chain.run(
                contact_name=contact.name,
                contact_title=contact.title,
                company_name=lead.company.consumer_facing_name,
                company_description=lead.company.description or "Multi-location dental practice",
                locations_count=lead.company.locations_count,
                states_served=", ".join(lead.company.states_served) if lead.company.states_served else "Multiple states",
                personalization_notes=personalization_notes
            )
            
            return email_content.strip()
            
        except Exception as e:
            print(f"Email generation error: {e}")
            return f"Error generating email: {str(e)}"
    
    def create_personalization_notes(self, lead: Lead) -> str:
        """
        Create personalization notes based on research
        """
        notes = []
        
        # Add company-specific insights
        if lead.company.locations_count > 5:
            notes.append(f"Impressive {lead.company.locations_count}-location operation")
        
        if len(lead.company.states_served) > 1:
            notes.append(f"Multi-state presence across {', '.join(lead.company.states_served)}")
        
        # Add contact-specific insights
        if lead.primary_contact:
            if 'ceo' in lead.primary_contact.title.lower():
                notes.append("As the CEO, you understand the importance of operational efficiency")
            elif 'operations' in lead.primary_contact.title.lower():
                notes.append("Your operations role puts you at the center of practice efficiency")
            elif 'it' in lead.primary_contact.title.lower():
                notes.append("Your IT background makes you perfect for evaluating AI solutions")
        
        # Add research notes
        if lead.research_notes:
            notes.append(lead.research_notes[:100])  # Limit to 100 chars
        
        return ". ".join(notes) if notes else "Growing dental organization with multiple locations"
    
    def save_email_draft(self, email_content: str, contact_email: str) -> bool:
        """
        Save email as draft (placeholder - would integrate with Gmail API)
        """
        try:
            # In a real implementation, this would use Gmail API to save as draft
            # For now, we'll just log it
            print(f"Email draft saved for {contact_email}")
            print(f"Content: {email_content[:100]}...")
            return True
        except Exception as e:
            print(f"Error saving email draft: {e}")
            return False
    
    def send_email(self, to_email: str, subject: str, body: str) -> bool:
        """
        Send email using SMTP (for testing purposes)
        """
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config.GMAIL_USERNAME
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.starttls()
            server.login(self.config.GMAIL_USERNAME, self.config.GMAIL_PASSWORD)
            
            text = msg.as_string()
            server.sendmail(self.config.GMAIL_USERNAME, to_email, text)
            server.quit()
            
            return True
            
        except Exception as e:
            print(f"Email sending error: {e}")
            return False
    
    def extract_subject_and_body(self, email_content: str) -> Dict[str, str]:
        """
        Extract subject and body from generated email content
        """
        lines = email_content.split('\n')
        subject = ""
        body = ""
        
        for i, line in enumerate(lines):
            if line.lower().startswith('subject:'):
                subject = line.replace('Subject:', '').replace('subject:', '').strip()
                body = '\n'.join(lines[i+1:]).strip()
                break
        
        if not subject:
            # If no subject line found, use first line as subject
            subject = lines[0] if lines else "VoiceCare AI - Dental Practice Automation"
            body = '\n'.join(lines[1:]).strip() if len(lines) > 1 else email_content
        
        return {
            'subject': subject,
            'body': body
        }
    
    def validate_email_content(self, email_content: str) -> bool:
        """
        Validate email content meets quality standards
        """
        # Check length
        if len(email_content) < 50 or len(email_content) > 2000:
            return False
        
        # Check for required elements
        required_elements = ['voicecare', 'dental', 'ai']
        content_lower = email_content.lower()
        
        has_required = all(element in content_lower for element in required_elements)
        
        return has_required
