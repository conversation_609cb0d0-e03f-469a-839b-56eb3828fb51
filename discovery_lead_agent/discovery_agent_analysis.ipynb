{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Discovery Lead Agent - Step-by-Step Analysis\n", "\n", "This notebook analyzes each step of the Discovery Lead Agent process and examines the outputs in detail.\n", "\n", "## Overview\n", "The agent follows a 6-step process:\n", "1. **Discovery** - Find potential leads using Google search\n", "2. **Research** - Scrape websites for company information\n", "3. **Qualification** - Score and filter leads based on criteria\n", "4. **HubSpot Integration** - Create CRM records (simulated)\n", "5. **Email Generation** - Create personalized outreach emails\n", "6. **Reporting** - Generate comprehensive results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import numpy as np\n", "from collections import Counter\n", "import re\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"📊 Libraries imported successfully!\")\n", "print(f\"Analysis started at: {datetime.now()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Load and Examine the Results Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the results from the demo run\n", "with open('demo_results_20250712_195756.json', 'r') as f:\n", "    results = json.load(f)\n", "\n", "print(\"🔍 DISCOVERY AGENT RESULTS OVERVIEW\")\n", "print(\"=\" * 50)\n", "print(f\"📅 Generated at: {results['summary']['generated_at']}\")\n", "print(f\"🏢 Total companies discovered: {results['summary']['total_discovered']}\")\n", "print(f\"✅ Qualified leads: {results['summary']['qualified_leads']}\")\n", "print(f\"📈 Success rate: {results['summary']['success_rate']}\")\n", "print(f\"📋 Number of detailed lead records: {len(results['leads'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Analyze Lead Discovery Process"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert leads to DataFrame for easier analysis\n", "leads_df = pd.DataFrame(results['leads'])\n", "\n", "print(\"📊 LEAD DISCOVERY ANALYSIS\")\n", "print(\"=\" * 40)\n", "print(f\"Total qualified leads: {len(leads_df)}\")\n", "print(\"\\n🏢 Company Names:\")\n", "for i, company in enumerate(leads_df['company_name'], 1):\n", "    print(f\"  {i}. {company}\")\n", "\n", "print(\"\\n🌐 Domains Found:\")\n", "for i, domain in enumerate(leads_df['domain'], 1):\n", "    print(f\"  {i}. {domain}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Analyze Qualification Scores"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze qualification scores\n", "scores = leads_df['qualification_score'].values\n", "\n", "print(\"🎯 QUALIFICATION SCORE ANALYSIS\")\n", "print(\"=\" * 40)\n", "print(f\"Average score: {np.mean(scores):.1f}/100\")\n", "print(f\"Median score: {np.median(scores):.1f}/100\")\n", "print(f\"Highest score: {np.max(scores)}/100\")\n", "print(f\"Lowest score: {np.min(scores)}/100\")\n", "print(f\"Standard deviation: {np.std(scores):.1f}\")\n", "\n", "# Create visualization\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Score distribution\n", "plt.subplot(1, 2, 1)\n", "plt.bar(range(len(scores)), scores, color='skyblue', alpha=0.7)\n", "plt.axhline(y=np.mean(scores), color='red', linestyle='--', label=f'Average: {np.mean(scores):.1f}')\n", "plt.xlabel('Lead Index')\n", "plt.ylabel('Qualification Score')\n", "plt.title('Qualification Scores by Lead')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Score histogram\n", "plt.subplot(1, 2, 2)\n", "plt.hist(scores, bins=5, color='lightgreen', alpha=0.7, edgecolor='black')\n", "plt.xlabel('Qualification Score')\n", "plt.ylabel('Number of Leads')\n", "plt.title('Distribution of Qualification Scores')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Detailed score breakdown\n", "print(\"\\n📋 DETAILED SCORE BREAKDOWN:\")\n", "for i, (company, score) in enumerate(zip(leads_df['company_name'], scores), 1):\n", "    print(f\"  {i}. {company[:50]}... → {score}/100\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Analyze Location Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze location information\n", "location_counts = leads_df['estimated_locations'].values\n", "\n", "print(\"📍 LOCATION ANALYSIS\")\n", "print(\"=\" * 30)\n", "print(f\"Average locations per company: {np.mean(location_counts):.1f}\")\n", "print(f\"Total locations across all leads: {np.sum(location_counts)}\")\n", "print(f\"Company with most locations: {np.max(location_counts)}\")\n", "print(f\"Company with least locations: {np.min(location_counts)}\")\n", "\n", "# Detailed location breakdown\n", "print(\"\\n🏢 LOCATION BREAKDOWN BY COMPANY:\")\n", "for i, (company, locations, found_locs) in enumerate(zip(\n", "    leads_df['company_name'], \n", "    leads_df['estimated_locations'],\n", "    leads_df['locations_found']\n", "), 1):\n", "    print(f\"\\n  {i}. {company[:40]}...\")\n", "    print(f\"     Estimated locations: {locations}\")\n", "    print(f\"     Location indicators found: {found_locs[:3]}...\")  # Show first 3\n", "\n", "# Visualize location distribution\n", "plt.figure(figsize=(10, 6))\n", "companies_short = [name[:20] + '...' for name in leads_df['company_name']]\n", "plt.barh(companies_short, location_counts, color='orange', alpha=0.7)\n", "plt.xlabel('Number of Locations')\n", "plt.title('Estimated Locations per Company')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Analyze Contact Information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze contact information\n", "print(\"📞 CONTACT INFORMATION ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "total_emails = 0\n", "total_phones = 0\n", "companies_with_emails = 0\n", "companies_with_phones = 0\n", "\n", "contact_details = []\n", "\n", "for i, (company, contact_info) in enumerate(zip(leads_df['company_name'], leads_df['contact_info']), 1):\n", "    emails = contact_info.get('emails', [])\n", "    phones = contact_info.get('phones', [])\n", "    \n", "    total_emails += len(emails)\n", "    total_phones += len(phones)\n", "    \n", "    if emails:\n", "        companies_with_emails += 1\n", "    if phones:\n", "        companies_with_phones += 1\n", "    \n", "    contact_details.append({\n", "        'company': company,\n", "        'email_count': len(emails),\n", "        'phone_count': len(phones),\n", "        'emails': emails,\n", "        'phones': phones\n", "    })\n", "    \n", "    print(f\"\\n  {i}. {company[:40]}...\")\n", "    print(f\"     📧 Emails found: {len(emails)}\")\n", "    if emails:\n", "        for email in emails:\n", "            print(f\"        • {email}\")\n", "    print(f\"     📱 Phones found: {len(phones)}\")\n", "    if phones:\n", "        for phone in phones:\n", "            print(f\"        • {phone}\")\n", "\n", "print(f\"\\n📊 CONTACT SUMMARY:\")\n", "print(f\"   Total emails found: {total_emails}\")\n", "print(f\"   Total phones found: {total_phones}\")\n", "print(f\"   Companies with emails: {companies_with_emails}/{len(leads_df)} ({companies_with_emails/len(leads_df)*100:.1f}%)\")\n", "print(f\"   Companies with phones: {companies_with_phones}/{len(leads_df)} ({companies_with_phones/len(leads_df)*100:.1f}%)\")\n", "\n", "# Visualize contact information\n", "contact_df = pd.DataFrame(contact_details)\n", "\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "companies_short = [name[:15] + '...' for name in contact_df['company']]\n", "plt.bar(companies_short, contact_df['email_count'], color='lightblue', alpha=0.7, label='Emails')\n", "plt.bar(companies_short, contact_df['phone_count'], bottom=contact_df['email_count'], \n", "        color='lightcoral', alpha=0.7, label='Phones')\n", "plt.xlabel('Companies')\n", "plt.y<PERSON><PERSON>('Contact Count')\n", "plt.title('Contact Information by Company')\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "\n", "plt.subplot(1, 2, 2)\n", "contact_types = ['Companies with Emails', 'Companies with Phones']\n", "contact_counts = [companies_with_emails, companies_with_phones]\n", "plt.pie(contact_counts, labels=contact_types, autopct='%1.1f%%', startangle=90)\n", "plt.title('Contact Information Coverage')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Analyze Generated Emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze generated emails\n", "print(\"✉️ EMAIL GENERATION ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "email_stats = []\n", "\n", "for i, (company, email) in enumerate(zip(leads_df['company_name'], leads_df['demo_email']), 1):\n", "    # Extract subject line\n", "    subject_match = re.search(r'Subject: (.+?)\\n', email)\n", "    subject = subject_match.group(1) if subject_match else \"No subject found\"\n", "    \n", "    # Calculate email metrics\n", "    word_count = len(email.split())\n", "    char_count = len(email)\n", "    line_count = len(email.split('\\n'))\n", "    \n", "    email_stats.append({\n", "        'company': company,\n", "        'subject': subject,\n", "        'word_count': word_count,\n", "        'char_count': char_count,\n", "        'line_count': line_count\n", "    })\n", "    \n", "    print(f\"\\n  {i}. {company[:40]}...\")\n", "    print(f\"     📧 Subject: {subject}\")\n", "    print(f\"     📝 Word count: {word_count}\")\n", "    print(f\"     📏 Character count: {char_count}\")\n", "\n", "# Email statistics\n", "email_df = pd.DataFrame(email_stats)\n", "avg_words = email_df['word_count'].mean()\n", "avg_chars = email_df['char_count'].mean()\n", "\n", "print(f\"\\n📊 EMAIL STATISTICS:\")\n", "print(f\"   Average word count: {avg_words:.1f}\")\n", "print(f\"   Average character count: {avg_chars:.1f}\")\n", "print(f\"   Total emails generated: {len(email_df)}\")\n", "\n", "# Visualize email metrics\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.bar(range(len(email_df)), email_df['word_count'], color='lightgreen', alpha=0.7)\n", "plt.axhline(y=avg_words, color='red', linestyle='--', label=f'Avg: {avg_words:.1f}')\n", "plt.xlabel('Email Index')\n", "plt.ylabel('Word Count')\n", "plt.title('Email Word Count')\n", "plt.legend()\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.bar(range(len(email_df)), email_df['char_count'], color='lightcoral', alpha=0.7)\n", "plt.axhline(y=avg_chars, color='red', linestyle='--', label=f'Avg: {avg_chars:.1f}')\n", "plt.xlabel('Email Index')\n", "plt.y<PERSON><PERSON>('Character Count')\n", "plt.title('Email Character Count')\n", "plt.legend()\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.bar(range(len(email_df)), email_df['line_count'], color='lightyellow', alpha=0.7)\n", "plt.xlabel('Email Index')\n", "plt.ylabel('Line Count')\n", "plt.title('Email Line Count')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Show Sample Email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display a sample email\n", "print(\"📧 SAMPLE GENERATED EMAIL\")\n", "print(\"=\" * 50)\n", "\n", "# Get the highest scoring lead's email\n", "best_lead_idx = leads_df['qualification_score'].idxmax()\n", "best_company = leads_df.loc[best_lead_idx, 'company_name']\n", "best_email = leads_df.loc[best_lead_idx, 'demo_email']\n", "best_score = leads_df.loc[best_lead_idx, 'qualification_score']\n", "\n", "print(f\"🏆 Best Qualified Lead: {best_company}\")\n", "print(f\"🎯 Qualification Score: {best_score}/100\")\n", "print(\"\\n\" + \"=\"*60)\n", "print(best_email)\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: HubSpot Integration Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze HubSpot simulation data\n", "print(\"📊 HUBSPOT INTEGRATION ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "hubspot_data = []\n", "total_contacts_created = 0\n", "\n", "for i, (company, hubspot_sim) in enumerate(zip(leads_df['company_name'], leads_df['hubspot_simulation']), 1):\n", "    company_id = hubspot_sim.get('company_id', 'N/A')\n", "    contacts_created = hubspot_sim.get('contacts_created', 0)\n", "    created_at = hubspot_sim.get('created_at', 'N/A')\n", "    \n", "    total_contacts_created += contacts_created\n", "    \n", "    hubspot_data.append({\n", "        'company': company,\n", "        'company_id': company_id,\n", "        'contacts_created': contacts_created,\n", "        'created_at': created_at\n", "    })\n", "    \n", "    print(f\"\\n  {i}. {company[:40]}...\")\n", "    print(f\"     🆔 HubSpot Company ID: {company_id}\")\n", "    print(f\"     👥 Contacts Created: {contacts_created}\")\n", "    print(f\"     📅 Created At: {created_at[:19]}\")\n", "\n", "print(f\"\\n📊 HUBSPOT SUMMARY:\")\n", "print(f\"   Total companies created: {len(hubspot_data)}\")\n", "print(f\"   Total contacts created: {total_contacts_created}\")\n", "print(f\"   Average contacts per company: {total_contacts_created/len(hubspot_data):.1f}\")\n", "\n", "# Visualize HubSpot data\n", "hubspot_df = pd.DataFrame(hubspot_data)\n", "\n", "plt.figure(figsize=(10, 6))\n", "companies_short = [name[:20] + '...' for name in hubspot_df['company']]\n", "plt.bar(companies_short, hubspot_df['contacts_created'], color='purple', alpha=0.7)\n", "plt.xlabel('Companies')\n", "plt.ylabel('Contacts Created')\n", "plt.title('HubSpot Contacts Created per Company')\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Overall Performance Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive performance summary\n", "print(\"🎯 OVERALL PERFORMANCE SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "# Calculate key metrics\n", "total_discovered = results['summary']['total_discovered']\n", "qualified_leads = results['summary']['qualified_leads']\n", "success_rate = (qualified_leads / total_discovered) * 100\n", "\n", "avg_score = leads_df['qualification_score'].mean()\n", "avg_locations = leads_df['estimated_locations'].mean()\n", "total_locations = leads_df['estimated_locations'].sum()\n", "\n", "companies_with_contact_info = len([1 for _, row in leads_df.iterrows() \n", "                                  if row['contact_info']['emails'] or row['contact_info']['phones']])\n", "\n", "print(f\"📊 DISCOVERY METRICS:\")\n", "print(f\"   • Companies Discovered: {total_discovered}\")\n", "print(f\"   • Qualified Leads: {qualified_leads}\")\n", "print(f\"   • Success Rate: {success_rate:.1f}%\")\n", "print(f\"   • Average Qualification Score: {avg_score:.1f}/100\")\n", "\n", "print(f\"\\n🏢 COMPANY METRICS:\")\n", "print(f\"   • Average Locations per Company: {avg_locations:.1f}\")\n", "print(f\"   • Total Locations Across All Leads: {total_locations}\")\n", "print(f\"   • Companies with Contact Info: {companies_with_contact_info}/{qualified_leads} ({companies_with_contact_info/qualified_leads*100:.1f}%)\")\n", "\n", "print(f\"\\n✉️ EMAIL METRICS:\")\n", "print(f\"   • Emails Generated: {len(leads_df)}\")\n", "print(f\"   • Average Email Length: {email_df['word_count'].mean():.1f} words\")\n", "print(f\"   • Email Generation Success Rate: 100%\")\n", "\n", "print(f\"\\n📊 HUBSPOT METRICS:\")\n", "print(f\"   • Company Records Created: {len(hubspot_data)}\")\n", "print(f\"   • Contact Records Created: {total_contacts_created}\")\n", "print(f\"   • Integration Success Rate: 100%\")\n", "\n", "# Create final summary visualization\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Discovery funnel\n", "funnel_data = [total_discovered, qualified_leads]\n", "funnel_labels = ['Discovered', 'Qualified']\n", "ax1.bar(funnel_labels, funnel_data, color=['lightblue', 'lightgreen'], alpha=0.7)\n", "ax1.set_title('Discovery Funnel')\n", "ax1.set_ylabel('Number of Companies')\n", "for i, v in enumerate(funnel_data):\n", "    ax1.text(i, v + 0.5, str(v), ha='center', va='bottom')\n", "\n", "# Qualification scores\n", "ax2.hist(leads_df['qualification_score'], bins=5, color='orange', alpha=0.7, edgecolor='black')\n", "ax2.axvline(avg_score, color='red', linestyle='--', label=f'Average: {avg_score:.1f}')\n", "ax2.set_title('Qualification Score Distribution')\n", "ax2.set_xlabel('Score')\n", "ax2.set_ylabel('Number of Leads')\n", "ax2.legend()\n", "\n", "# Location distribution\n", "ax3.scatter(range(len(leads_df)), leads_df['estimated_locations'], \n", "           s=leads_df['qualification_score']*2, alpha=0.6, c=leads_df['qualification_score'], \n", "           cmap='viridis')\n", "ax3.set_title('Locations vs Qualification Score')\n", "ax3.set_xlabel('Lead Index')\n", "ax3.set_ylabel('Estimated Locations')\n", "cbar = plt.colorbar(ax3.collections[0], ax=ax3)\n", "cbar.set_label('Qualification Score')\n", "\n", "# Contact information pie chart\n", "contact_categories = ['With Emails & Phones', 'With Emails Only', 'With Phones Only', 'No Contact Info']\n", "contact_counts = [0, 0, 0, 0]\n", "\n", "for _, row in leads_df.iterrows():\n", "    has_emails = bool(row['contact_info']['emails'])\n", "    has_phones = bool(row['contact_info']['phones'])\n", "    \n", "    if has_emails and has_phones:\n", "        contact_counts[0] += 1\n", "    elif has_emails:\n", "        contact_counts[1] += 1\n", "    elif has_phones:\n", "        contact_counts[2] += 1\n", "    else:\n", "        contact_counts[3] += 1\n", "\n", "ax4.pie([c for c in contact_counts if c > 0], \n", "        labels=[l for l, c in zip(contact_categories, contact_counts) if c > 0],\n", "        autopct='%1.1f%%', startangle=90)\n", "ax4.set_title('Contact Information Coverage')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n🎉 ANALYSIS COMPLETE!\")\n", "print(\"The Discovery Lead Agent successfully demonstrated all 6 steps of the process.\")"]}], "metadata": {"kernelspec": {"display_name": "codechakra", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}