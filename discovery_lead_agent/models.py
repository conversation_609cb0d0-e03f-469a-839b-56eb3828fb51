"""
Data models for the Discovery Lead Agent
"""
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict
from datetime import datetime

class Contact(BaseModel):
    """Model for contact information"""
    name: str
    title: str
    email: Optional[str] = None
    linkedin_url: Optional[str] = None
    department: Optional[str] = None
    verified: bool = False

class Company(BaseModel):
    """Model for company information"""
    name: str
    consumer_facing_name: str
    domain: str
    website: str
    locations_count: int
    states_served: List[str] = []
    description: Optional[str] = None
    industry: str = "Dental Services"
    contacts: List[Contact] = []
    hubspot_id: Optional[str] = None
    
class Lead(BaseModel):
    """Model for lead information"""
    company: Company
    primary_contact: Optional[Contact] = None
    research_notes: str = ""
    email_draft: Optional[str] = None
    status: str = "discovered"  # discovered, researched, contacted, qualified
    created_at: datetime = Field(default_factory=datetime.now)
    
class EmailTemplate(BaseModel):
    """Model for email templates"""
    subject: str
    body: str
    personalization_fields: List[str] = []
    
class AgentLog(BaseModel):
    """Model for agent activity logging"""
    timestamp: datetime = Field(default_factory=datetime.now)
    action: str
    details: Dict
    success: bool
    error_message: Optional[str] = None
