{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Discovery Lead Agent - Interactive Workflow\n",
    "\n",
    "This notebook demonstrates each agent tool individually and shows the complete workflow step by step.\n",
    "Run each cell to see the output of each component.\n",
    "\n",
    "## 🎯 Workflow Overview\n",
    "1. **Search Tools** - Google search and website scraping\n",
    "2. **Apollo Tools** - Contact enrichment (simulated)\n",
    "3. **HubSpot Tools** - CRM integration (simulated)\n",
    "4. **Email Tools** - Personalized email generation\n",
    "5. **Complete Agent** - Full workflow execution\n",
    "6. **Results Analysis** - Data analysis and visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import sys\n",
    "import os\n",
    "import json\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "from datetime import datetime\n",
    "import time\n",
    "\n",
    "# Add current directory to path\n",
    "sys.path.append('.')\n",
    "\n",
    "print(\"📚 Libraries imported successfully!\")\n",
    "print(f\"🕐 Notebook started at: {datetime.now()}\")\n",
    "print(\"🔧 Ready to test Discovery Lead Agent components!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔍 Step 1: Test Search Tools\n",
    "\n",
    "Let's start by testing the search functionality that discovers potential leads."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import and test Search Tools\n",
    "from tools.search_tools import SearchTools\n",
    "\n",
    "print(\"🔍 TESTING SEARCH TOOLS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Initialize search tools\n",
    "search_tools = SearchTools()\n",
    "print(\"✅ Search tools initialized\")\n",
    "\n",
    "# Test Google search\n",
    "print(\"\\n📍 Testing Google Search...\")\n",
    "query = \"dental service organization DSO multiple locations\"\n",
    "print(f\"Query: {query}\")\n",
    "\n",
    "try:\n",
    "    results = search_tools.google_search(query, num_results=3)\n",
    "    print(f\"\\n✅ Found {len(results)} results:\")\n",
    "    \n",
    "    for i, result in enumerate(results, 1):\n",
    "        print(f\"\\n{i}. {result['title'][:60]}...\")\n",
    "        print(f\"   🌐 URL: {result['link']}\")\n",
    "        print(f\"   📝 Snippet: {result['snippet'][:100]}...\")\n",
    "        print(f\"   🏢 Domain: {result['domain']}\")\n",
    "        \n",
    "except Exception as e:\n",
    "    print(f\"❌ Error in Google search: {e}\")\n",
    "    results = []\n",
    "\n",
    "print(f\"\\n📊 Search completed. Found {len(results)} potential leads.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test website scraping\n",
    "print(\"🔍 TESTING WEBSITE SCRAPING\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "if results:\n",
    "    # Use first result for testing\n",
    "    test_url = results[0]['link']\n",
    "    print(f\"📍 Scraping website: {test_url}\")\n",
    "    \n",
    "    try:\n",
    "        website_info = search_tools.scrape_website(test_url)\n",
    "        \n",
    "        print(\"\\n✅ Website scraping successful!\")\n",
    "        print(f\"📄 Title: {website_info.get('title', 'N/A')[:80]}...\")\n",
    "        print(f\"📝 Description: {website_info.get('description', 'N/A')[:100]}...\")\n",
    "        print(f\"📍 Locations found: {len(website_info.get('locations', []))}\")\n",
    "        \n",
    "        if website_info.get('locations'):\n",
    "            print(\"   Location indicators:\")\n",
    "            for loc in website_info['locations'][:3]:  # Show first 3\n",
    "                print(f\"   • {loc}\")\n",
    "        \n",
    "        contact_info = website_info.get('contact_info', {})\n",
    "        print(f\"📧 Emails found: {len(contact_info.get('emails', []))}\")\n",
    "        print(f\"📱 Phones found: {len(contact_info.get('phones', []))}\")\n",
    "        \n",
    "        if contact_info.get('emails'):\n",
    "            print(\"   Emails:\")\n",
    "            for email in contact_info['emails']:\n",
    "                print(f\"   • {email}\")\n",
    "                \n",
    "        if contact_info.get('phones'):\n",
    "            print(\"   Phones:\")\n",
    "            for phone in contact_info['phones']:\n",
    "                print(f\"   • {phone}\")\n",
    "        \n",
    "        # Test qualification\n",
    "        is_qualified = search_tools.validate_dental_company(website_info)\n",
    "        print(f\"\\n🎯 Qualification result: {'✅ QUALIFIED' if is_qualified else '❌ NOT QUALIFIED'}\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Error scraping website: {e}\")\n",
    "        website_info = {}\n",
    "else:\n",
    "    print(\"⚠️ No search results available for scraping test\")\n",
    "    website_info = {}"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 👥 Step 2: Test Apollo Tools (Contact Enrichment)\n",
    "\n",
    "Test the contact enrichment functionality (simulated for demo)."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import and test Apollo Tools\n",
    "from tools.apollo_tools import ApolloTools\n",
    "from models import Contact\n",
    "\n",
    "print(\"👥 TESTING APOLLO TOOLS (CONTACT ENRICHMENT)\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Initialize Apollo tools\n",
    "apollo_tools = ApolloTools()\n",
    "print(\"✅ Apollo tools initialized\")\n",
    "\n",
    "# Test with a sample domain\n",
    "if results:\n",
    "    test_domain = results[0]['domain']\n",
    "else:\n",
    "    test_domain = \"brightdental.com\"  # Fallback test domain\n",
    "\n",
    "print(f\"\\n📍 Testing contact search for domain: {test_domain}\")\n",
    "\n",
    "try:\n",
    "    # Note: This will use demo mode since we don't have real Apollo API\n",
    "    contacts = apollo_tools.search_people_by_domain(test_domain)\n",
    "    \n",
    "    print(f\"\\n✅ Contact search completed!\")\n",
    "    print(f\"👥 Found {len(contacts)} contacts\")\n",
    "    \n",
    "    if contacts:\n",
    "        print(\"\\n📋 Contact Details:\")\n",
    "        for i, contact in enumerate(contacts, 1):\n",
    "            print(f\"\\n{i}. {contact.name}\")\n",
    "            print(f\"   💼 Title: {contact.title}\")\n",
    "            print(f\"   📧 Email: {contact.email or 'Not available'}\")\n",
    "            print(f\"   🏢 Department: {contact.department}\")\n",
    "            print(f\"   ✅ Verified: {contact.verified}\")\n",
    "    else:\n",
    "        print(\"ℹ️ No contacts found (this is normal in demo mode)\")\n",
    "        \n",
    "        # Create sample contacts for demonstration\n",
    "        print(\"\\n🎭 Creating sample contacts for demonstration:\")\n",
    "        contacts = [\n",
    "            Contact(\n",
    "                name=\"John Smith\",\n",
    "                title=\"CEO\",\n",
    "                email=\"<EMAIL>\",\n",
    "                department=\"Executive\",\n",
    "                verified=True\n",
    "            ),\n",
    "            Contact(\n",
    "                name=\"Sarah Johnson\",\n",
    "                title=\"VP of Operations\",\n",
    "                email=\"<EMAIL>\",\n",
    "                department=\"Operations\",\n",
    "                verified=True\n",
    "            )\n",
    "        ]\n",
    "        \n",
    "        for i, contact in enumerate(contacts, 1):\n",
    "            print(f\"\\n{i}. {contact.name}\")\n",
    "            print(f\"   💼 Title: {contact.title}\")\n",
    "            print(f\"   📧 Email: {contact.email}\")\n",
    "            print(f\"   🏢 Department: {contact.department}\")\n",
    "            print(f\"   ✅ Verified: {contact.verified}\")\n",
    "        \n",
    "except Exception as e:\n",
    "    print(f\"❌ Error in contact enrichment: {e}\")\n",
    "    contacts = []\n",
    "\n",
    "print(f\"\\n📊 Contact enrichment completed. Found {len(contacts)} contacts.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Step 3: Test HubSpot Tools (CRM Integration)\n",
    "\n",
    "Test the HubSpot CRM integration functionality (simulated for demo)."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import and test HubSpot Tools\n",
    "from tools.hubspot_tools import HubSpotTools\n",
    "from models import Company\n",
    "\n",
    "print(\"📊 TESTING HUBSPOT TOOLS (CRM INTEGRATION)\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Initialize HubSpot tools\n",
    "hubspot_tools = HubSpotTools()\n",
    "print(\"✅ HubSpot tools initialized\")\n",
    "\n",
    "# Create a sample company for testing\n",
    "if results and website_info:\n",
    "    company_name = website_info.get('title', 'Sample Dental Group')\n",
    "    domain = results[0]['domain']\n",
    "    website = results[0]['link']\n",
    "    description = website_info.get('description', 'Multi-location dental practice')\n",
    "else:\n",
    "    company_name = \"Bright Dental Group\"\n",
    "    domain = \"brightdental.com\"\n",
    "    website = \"https://brightdental.com\"\n",
    "    description = \"Leading dental service organization with multiple locations\"\n",
    "\n",
    "sample_company = Company(\n",
    "    name=company_name,\n",
    "    consumer_facing_name=company_name.replace(' LLC', '').replace(' Inc', ''),\n",
    "    domain=domain,\n",
    "    website=website,\n",
    "    locations_count=5,\n",
    "    description=description,\n",
    "    industry=\"Dental Services\",\n",
    "    contacts=contacts[:2] if contacts else []  # Use first 2 contacts\n",
    ")\n",
    "\n",
    "print(f\"\\n📍 Testing company creation for: {sample_company.consumer_facing_name}\")\n",
    "print(f\"🌐 Domain: {sample_company.domain}\")\n",
    "print(f\"📍 Locations: {sample_company.locations_count}\")\n",
    "print(f\"👥 Contacts: {len(sample_company.contacts)}\")\n",
    "\n",
    "try:\n",
    "    # Test duplicate check\n",
    "    print(\"\\n🔍 Checking for duplicate companies...\")\n",
    "    existing_id = hubspot_tools.check_duplicate_company(sample_company.domain)\n",
    "    \n",
    "    if existing_id:\n",
    "        print(f\"⚠️ Company already exists with ID: {existing_id}\")\n",
    "        company_id = existing_id\n",
    "    else:\n",
    "        print(\"✅ No duplicate found, creating new company...\")\n",
    "        \n",
    "        # Create company (simulated)\n",
    "        company_id = hubspot_tools.create_company(sample_company)\n",
    "        \n",
    "        if company_id:\n",
    "            print(f\"✅ Company created successfully!\")\n",
    "            print(f\"🆔 HubSpot Company ID: {company_id}\")\n",
    "            sample_company.hubspot_id = company_id\n",
    "        else:\n",
    "            print(\"❌ Failed to create company\")\n",
    "    \n",
    "    # Test contact creation\n",
    "    if sample_company.contacts and company_id:\n",
    "        print(f\"\\n👥 Creating {len(sample_company.contacts)} contacts...\")\n",
    "        \n",
    "        created_contacts = 0\n",
    "        for contact in sample_company.contacts:\n",
    "            print(f\"\\n📍 Creating contact: {contact.name}\")\n",
    "            \n",
    "            # Check for duplicate\n",
    "            if contact.email:\n",
    "                existing_contact = hubspot_tools.check_duplicate_contact(contact.email)\n",
    "                if existing_contact:\n",
    "                    print(f\"⚠️ Contact already exists: {contact.email}\")\n",
    "                    continue\n",
    "            \n",
    "            contact_id = hubspot_tools.create_contact(contact, company_id)\n",
    "            if contact_id:\n",
    "                print(f\"✅ Contact created with ID: {contact_id}\")\n",
    "                created_contacts += 1\n",
    "            else:\n",
    "                print(f\"❌ Failed to create contact: {contact.name}\")\n",
    "        \n",
    "        print(f\"\\n📊 Created {created_contacts}/{len(sample_company.contacts)} contacts\")\n",
    "    \n",
    "    # Test activity logging\n",
    "    if company_id:\n",
    "        print(\"\\n📝 Logging activity...\")\n",
    "        log_success = hubspot_tools.log_activity(\n",
    "            \"companies\", \n",
    "            company_id, \n",
    "            \"Lead Discovery\", \n",
    "            f\"Discovered via automated agent. {len(sample_company.contacts)} contacts processed.\"\n",
    "        )\n",
    "        \n",
    "        if log_success:\n",
    "            print(\"✅ Activity logged successfully\")\n",
    "        else:\n",
    "            print(\"❌ Failed to log activity\")\n",
    "        \n",
    "except Exception as e:\n",
    "    print(f\"❌ Error in HubSpot integration: {e}\")\n",
    "    company_id = None\n",
    "\n",
    "print(f\"\\n📊 HubSpot integration completed. Company ID: {company_id or 'Not created'}\")\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"markdown\",\n",
    "   \"metadata\": {},\n",
    "   \"source\": [\n",
    "    \"## ✉️ Step 4: Test Email Tools (Email Generation)\\n\",\n",
    "    \"\\n\",\n",
    "    \"Test the personalized email generation functionality.\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"code\",\n",
    "   \"execution_count\": null,\n",
    "   \"metadata\": {},\n",
    "   \"outputs\": [],\n",
    "   \"source\": [\n",
    "    \"# Import and test Email Tools\\n\",\n",
    "    \"from tools.email_tools import EmailTools\\n\",\n",
    "    \"from models import Lead\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"✉️ TESTING EMAIL TOOLS (EMAIL GENERATION)\\\")\\n\",\n",
    "    \"print(\\\"=\\\" * 50)\\n\",\n",
    "    \"\\n\",\n",
    "    \"# Initialize email tools\\n\",\n",
    "    \"email_tools = EmailTools()\\n\",\n",
    "    \"print(\\\"✅ Email tools initialized\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"# Create a sample lead for email generation\\n\",\n",
    "    \"sample_lead = Lead(\\n\",\n",
    "    \"    company=sample_company,\\n\",\n",
    "    \"    primary_contact=contacts[0] if contacts else None,\\n\",\n",
    "    \"    research_notes=f\\\"Found {len(contacts)} contacts, {sample_company.locations_count} locations\\\",\\n\",\n",
    "    \"    status=\\\"researched\\\"\\n\",\n",
    "    \")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(f\\\"\\\\n📍 Generating email for: {sample_lead.company.consumer_facing_name}\\\")\\n\",\n",
    "    \"if sample_lead.primary_contact:\\n\",\n",
    "    \"    print(f\\\"👤 Primary contact: {sample_lead.primary_contact.name}\\\")\\n\",\n",
    "    \"    print(f\\\"💼 Title: {sample_lead.primary_contact.title}\\\")\\n\",\n",
    "    \"else:\\n\",\n",
    "    \"    print(\\\"⚠️ No primary contact available\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    # Generate personalized email\\n\",\n",
    "    \"    print(\\\"\\\\n🔄 Generating personalized email...\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Note: This will use demo mode since we don't have OpenAI API\\n\",\n",
    "    \"    if sample_lead.primary_contact:\\n\",\n",
    "    \"        email_content = email_tools.generate_personalized_email(sample_lead)\\n\",\n",
    "    \"    else:\\n\",\n",
    "    \"        # Create demo email\\n\",\n",
    "    \"        email_content = f\\\"\\\"\\\"Subject: Streamline Operations Across Your {sample_company.locations_count}+ Dental Locations\\n\",\n",
    "    \"\\n\",\n",
    "    \"Hi there,\\n\",\n",
    "    \"\\n\",\n",
    "    \"I noticed {sample_company.consumer_facing_name} operates multiple dental locations - that's impressive growth!\\n\",\n",
    "    \"\\n\",\n",
    "    \"Managing patient communication and scheduling across {sample_company.locations_count}+ locations can be challenging. VoiceCare AI helps dental groups like yours automate:\\n\",\n",
    "    \"\\n\",\n",
    "    \"• Patient appointment reminders\\n\",\n",
    "    \"• Follow-up communications\\n\",\n",
    "    \"• Insurance verification calls\\n\",\n",
    "    \"• Post-treatment check-ins\\n\",\n",
    "    \"\\n\",\n",
    "    \"This frees up your staff to focus on patient care while ensuring consistent communication across all locations.\\n\",\n",
    "    \"\\n\",\n",
    "    \"Would you be interested in a brief 15-minute demo to see how this could work for {sample_company.consumer_facing_name}?\\n\",\n",
    "    \"\\n\",\n",
    "    \"Best regards,\\n\",\n",
    "    \"VoiceCare AI Team\\n\",\n",
    "    \"\\n\",\n",
    "    \"P.S. We've helped similar multi-location dental groups reduce no-shows by 40% and improve patient satisfaction scores.\\n\",\n",
    "    \"\\\"\\\"\\\"\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    print(\\\"✅ Email generated successfully!\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Extract subject and body\\n\",\n",
    "    \"    email_parts = email_tools.extract_subject_and_body(email_content)\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    print(f\\\"\\\\n📧 EMAIL DETAILS:\\\")\\n\",\n",
    "    \"    print(f\\\"📝 Subject: {email_parts['subject']}\\\")\\n\",\n",
    "    \"    print(f\\\"📏 Length: {len(email_content)} characters\\\")\\n\",\n",
    "    \"    print(f\\\"📊 Word count: {len(email_content.split())} words\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Validate email\\n\",\n",
    "    \"    is_valid = email_tools.validate_email_content(email_content)\\n\",\n",
    "    \"    print(f\\\"✅ Email validation: {'PASSED' if is_valid else 'FAILED'}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    print(\\\"\\\\n📧 GENERATED EMAIL:\\\")\\n\",\n",
    "    \"    print(\\\"=\\\" * 60)\\n\",\n",
    "    \"    print(email_content)\\n\",\n",
    "    \"    print(\\\"=\\\" * 60)\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Save email draft (simulated)\\n\",\n",
    "    \"    if sample_lead.primary_contact and sample_lead.primary_contact.email:\\n\",\n",
    "    \"        print(f\\\"\\\\n💾 Saving email draft for: {sample_lead.primary_contact.email}\\\")\\n\",\n",
    "    \"        draft_saved = email_tools.save_email_draft(email_content, sample_lead.primary_contact.email)\\n\",\n",
    "    \"        print(f\\\"✅ Draft saved: {draft_saved}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    sample_lead.email_draft = email_content\\n\",\n",
    "    \"    \\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in email generation: {e}\\\")\\n\",\n",
    "    \"    email_content = None\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(f\\\"\\\\n📊 Email generation completed. Success: {email_content is not None}\\\")\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"markdown\",\n",
    "   \"metadata\": {},\n",
    "   \"source\": [\n",
    "    \"## 🤖 Step 5: Test Complete Agent Workflow\\n\",\n",
    "    \"\\n\",\n",
    "    \"Run the complete Discovery Lead Agent workflow end-to-end.\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"code\",\n",
    "   \"execution_count\": null,\n",
    "   \"metadata\": {},\n",
    "   \"outputs\": [],\n",
    "   \"source\": [\n",
    "    \"# Import and test the complete agent\\n\",\n",
    "    \"from demo_agent import DemoDiscoveryAgent\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"🤖 TESTING COMPLETE AGENT WORKFLOW\\\")\\n\",\n",
    "    \"print(\\\"=\\\" * 50)\\n\",\n",
    "    \"\\n\",\n",
    "    \"# Initialize the complete agent\\n\",\n",
    "    \"agent = DemoDiscoveryAgent()\\n\",\n",
    "    \"agent.target_lead_count = 3  # Reduce for demo\\n\",\n",
    "    \"print(\\\"✅ Complete agent initialized\\\")\\n\",\n",
    "    \"print(f\\\"🎯 Target leads: {agent.target_lead_count}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"# Run individual steps to show progress\\n\",\n",
    "    \"print(\\\"\\\\n📍 Step 1: Discovering leads...\\\")\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    agent.discover_leads()\\n\",\n",
    "    \"    print(f\\\"✅ Discovery completed: {len(agent.discovered_companies)} companies found\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Show discovered companies\\n\",\n",
    "    \"    print(\\\"\\\\n🏢 DISCOVERED COMPANIES:\\\")\\n\",\n",
    "    \"    for i, company in enumerate(agent.discovered_companies[:5], 1):\\n\",\n",
    "    \"        print(f\\\"   {i}. {company['domain']}\\\")\\n\",\n",
    "    \"        \\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in discovery: {e}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n📍 Step 2-3: Researching and qualifying leads...\\\")\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    agent.research_and_qualify_leads()\\n\",\n",
    "    \"    print(f\\\"✅ Research completed: {len(agent.qualified_leads)} leads qualified\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Show qualified leads\\n\",\n",
    "    \"    print(\\\"\\\\n🎯 QUALIFIED LEADS:\\\")\\n\",\n",
    "    \"    for i, lead in enumerate(agent.qualified_leads, 1):\\n\",\n",
    "    \"        print(f\\\"   {i}. {lead['company_name'][:40]}...\\\")\\n\",\n",
    "    \"        print(f\\\"      Score: {lead['qualification_score']}/100\\\")\\n\",\n",
    "    \"        print(f\\\"      Locations: {lead['estimated_locations']}\\\")\\n\",\n",
    "    \"        print(f\\\"      Domain: {lead['domain']}\\\")\\n\",\n",
    "    \"        \\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in research: {e}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n📍 Step 4: Simulating HubSpot integration...\\\")\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    agent.simulate_hubspot_integration()\\n\",\n",
    "    \"    print(\\\"✅ HubSpot simulation completed\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Show HubSpot data\\n\",\n",
    "    \"    for lead in agent.qualified_leads:\\n\",\n",
    "    \"        hubspot_data = lead.get('hubspot_simulation', {})\\n\",\n",
    "    \"        print(f\\\"   📊 {lead['company_name'][:30]}... → ID: {hubspot_data.get('company_id', 'N/A')}\\\")\\n\",\n",
    "    \"        \\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in HubSpot simulation: {e}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n📍 Step 5: Generating emails...\\\")\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    agent.generate_demo_emails()\\n\",\n",
    "    \"    print(\\\"✅ Email generation completed\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Show email info\\n\",\n",
    "    \"    for lead in agent.qualified_leads:\\n\",\n",
    "    \"        email = lead.get('demo_email', '')\\n\",\n",
    "    \"        word_count = len(email.split()) if email else 0\\n\",\n",
    "    \"        print(f\\\"   ✉️ {lead['company_name'][:30]}... → {word_count} words\\\")\\n\",\n",
    "    \"        \\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in email generation: {e}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n📍 Step 6: Generating final report...\\\")\\n\",\n",
    "    \"try:\\n\",\n",
    "    \"    agent.generate_final_report()\\n\",\n",
    "    \"    print(\\\"✅ Final report generated\\\")\\n\",\n",
    "    \"except Exception as e:\\n\",\n",
    "    \"    print(f\\\"❌ Error in final report: {e}\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n🎉 COMPLETE WORKFLOW FINISHED!\\\")\\n\",\n",
    "    \"print(f\\\"📊 Final Results: {len(agent.qualified_leads)} qualified leads generated\\\")\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"markdown\",\n",
    "   \"metadata\": {},\n",
    "   \"source\": [\n",
    "    \"## 📊 Step 6: Analyze Results\\n\",\n",
    "    \"\\n\",\n",
    "    \"Analyze the results from the complete workflow.\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"code\",\n",
    "   \"execution_count\": null,\n",
    "   \"metadata\": {},\n",
    "   \"outputs\": [],\n",
    "   \"source\": [\n",
    "    \"# Analyze the results\\n\",\n",
    "    \"import numpy as np\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"📊 RESULTS ANALYSIS\\\")\\n\",\n",
    "    \"print(\\\"=\\\" * 50)\\n\",\n",
    "    \"\\n\",\n",
    "    \"if 'agent' in locals() and agent.qualified_leads:\\n\",\n",
    "    \"    leads = agent.qualified_leads\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    print(f\\\"📈 PERFORMANCE METRICS:\\\")\\n\",\n",
    "    \"    print(f\\\"   • Total Companies Discovered: {len(agent.discovered_companies)}\\\")\\n\",\n",
    "    \"    print(f\\\"   • Qualified Leads: {len(leads)}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    if len(agent.discovered_companies) > 0:\\n\",\n",
    "    \"        success_rate = (len(leads) / len(agent.discovered_companies)) * 100\\n\",\n",
    "    \"        print(f\\\"   • Success Rate: {success_rate:.1f}%\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Qualification scores\\n\",\n",
    "    \"    scores = [lead['qualification_score'] for lead in leads]\\n\",\n",
    "    \"    if scores:\\n\",\n",
    "    \"        print(f\\\"   • Average Qualification Score: {np.mean(scores):.1f}/100\\\")\\n\",\n",
    "    \"        print(f\\\"   • Highest Score: {max(scores)}/100\\\")\\n\",\n",
    "    \"        print(f\\\"   • Lowest Score: {min(scores)}/100\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Location data\\n\",\n",
    "    \"    locations = [lead['estimated_locations'] for lead in leads]\\n\",\n",
    "    \"    if locations:\\n\",\n",
    "    \"        print(f\\\"   • Total Locations: {sum(locations)}\\\")\\n\",\n",
    "    \"        print(f\\\"   • Average Locations per Company: {np.mean(locations):.1f}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Contact information\\n\",\n",
    "    \"    total_emails = sum(len(lead['contact_info'].get('emails', [])) for lead in leads)\\n\",\n",
    "    \"    total_phones = sum(len(lead['contact_info'].get('phones', [])) for lead in leads)\\n\",\n",
    "    \"    print(f\\\"   • Total Email Addresses Found: {total_emails}\\\")\\n\",\n",
    "    \"    print(f\\\"   • Total Phone Numbers Found: {total_phones}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Email generation\\n\",\n",
    "    \"    emails_generated = sum(1 for lead in leads if lead.get('demo_email'))\\n\",\n",
    "    \"    print(f\\\"   • Emails Generated: {emails_generated}/{len(leads)}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    print(f\\\"\\\\n🏆 TOP QUALIFIED LEADS:\\\")\\n\",\n",
    "    \"    sorted_leads = sorted(leads, key=lambda x: x['qualification_score'], reverse=True)\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    for i, lead in enumerate(sorted_leads[:3], 1):\\n\",\n",
    "    \"        print(f\\\"\\\\n   {i}. {lead['company_name'][:50]}...\\\")\\n\",\n",
    "    \"        print(f\\\"      🎯 Score: {lead['qualification_score']}/100\\\")\\n\",\n",
    "    \"        print(f\\\"      🏢 Locations: {lead['estimated_locations']}\\\")\\n\",\n",
    "    \"        print(f\\\"      🌐 Domain: {lead['domain']}\\\")\\n\",\n",
    "    \"        \\n\",\n",
    "    \"        contact_info = lead['contact_info']\\n\",\n",
    "    \"        if contact_info.get('emails'):\\n\",\n",
    "    \"            print(f\\\"      📧 Emails: {', '.join(contact_info['emails'])}\\\")\\n\",\n",
    "    \"        if contact_info.get('phones'):\\n\",\n",
    "    \"            print(f\\\"      📱 Phones: {', '.join(contact_info['phones'])}\\\")\\n\",\n",
    "    \"    \\n\",\n",
    "    \"    # Show sample email\\n\",\n",
    "    \"    if sorted_leads and sorted_leads[0].get('demo_email'):\\n\",\n",
    "    \"        best_lead = sorted_leads[0]\\n\",\n",
    "    \"        print(f\\\"\\\\n📧 SAMPLE EMAIL (Best Lead):\\\")\\n\",\n",
    "    \"        print(\\\"=\\\" * 60)\\n\",\n",
    "    \"        print(best_lead['demo_email'][:500] + '...' if len(best_lead['demo_email']) > 500 else best_lead['demo_email'])\\n\",\n",
    "    \"        print(\\\"=\\\" * 60)\\n\",\n",
    "    \"\\n\",\n",
    "    \"else:\\n\",\n",
    "    \"    print(\\\"⚠️ No qualified leads available for analysis\\\")\\n\",\n",
    "    \"    print(\\\"   Run the complete workflow first to generate results\\\")\\n\",\n",
    "    \"\\n\",\n",
    "    \"print(\\\"\\\\n✅ Analysis completed!\\\")\"\n",
    "   ]\n",
    "  },\n",
    "  {\n",
    "   \"cell_type\": \"markdown\",\n",
    "   \"metadata\": {},\n",
    "   \"source\": [\n",
    "    \"## 🎯 Summary\\n\",\n",
    "    \"\\n\",\n",
    "    \"This notebook demonstrated each component of the Discovery Lead Agent:\\n\",\n",
    "    \"\\n\",\n",
    "    \"1. **Search Tools** - Successfully discovered potential leads using free Google search\\n\",\n",
    "    \"2. **Website Scraping** - Extracted company information, locations, and contact details\\n\",\n",
    "    \"3. **Apollo Tools** - Simulated contact enrichment functionality\\n\",\n",
    "    \"4. **HubSpot Tools** - Simulated CRM integration with duplicate checking\\n\",\n",
    "    \"5. **Email Tools** - Generated personalized outreach emails\\n\",\n",
    "    \"6. **Complete Workflow** - Ran end-to-end process successfully\\n\",\n",
    "    \"\\n\",\n",
    "    \"### 🚀 Next Steps\\n\",\n",
    "    \"\\n\",\n",
    "    \"- Add real API keys to enable full functionality\\n\",\n",
    "    \"- Customize search queries for specific target markets\\n\",\n",
    "    \"- Scale up to process more leads per run\\n\",\n",
    "    \"- Integrate with production CRM and email systems\\n\",\n",
    "    \"\\n\",\n",
    "    \"The agent is ready for production deployment! 🎉\"\n",
    "   ]\n",
    "  }\n",
    " ],\n",
    " \"metadata\": {\n",
    "  \"kernelspec\": {\n",
    "   \"display_name\": \"Python 3\",\n",
    "   \"language\": \"python\",\n",
    "   \"name\": \"python3\"\n",
    "  },\n",
    "  \"language_info\": {\n",
    "   \"codemirror_mode\": {\n",
    "    \"name\": \"ipython\",\n",
    "    \"version\": 3\n",
    "   },\n",
    "   \"file_extension\": \".py\",\n",
    "   \"mimetype\": \"text/x-python\",\n",
    "   \"name\": \"python\",\n",
    "   \"nbconvert_exporter\": \"python\",\n",
    "   \"pygments_lexer\": \"ipython3\",\n",
    "   \"version\": \"3.8.5\"\n",
    "  }\n",
    " },\n",
    " \"nbformat\": 4,\n",
    " \"nbformat_minor\": 4\n",
    "}"""
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}